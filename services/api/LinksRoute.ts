import {
  PeriodType,
  UserAnalyticsQueryDto,
  UserAnalyticsResponseDto,
  UserLinkResponseDto,
  UserLinksQueryDto,
} from "./data-contracts";

export namespace Links {
  /**
   * Get all user links
   * @tags Links
   * @name LinksControllerGetUserLinks
   * @request GET:/links
   * @secure
   * @response `200` `PaginationResponseDto<UserLinkResponseDto>`
   * @response `401` `void` Unauthorized
   */
  export namespace LinksControllerGetUserLinks {
    export type RequestParams = {};
    export type RequestQuery = UserLinksQueryDto;
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = {
      items: UserLinkResponseDto[];
      total: number;
      page: number;
      pages: number;
      limit: number;
    };
  }

  /**
   * Generate a new link
   * @tags Links
   * @name LinksControllerGenerateLink
   * @request POST:/links/generate
   * @secure
   * @response `201` `{ shortUrl: string }`
   * @response `401` `void` Unauthorized
   */
  export namespace LinksControllerGenerateLink {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = { url: string };
    export type RequestHeaders = {};
    export type ResponseBody = { shortUrl: string };
  }

  /**
   * Get user analytics
   * @tags Links
   * @name LinksControllerGetUserAnalytics
   * @request GET:/links/analytics
   * @secure
   * @response `200` `UserAnalyticsResponseDto`
   * @response `401` `void` Unauthorized
   */
  export namespace LinksControllerGetUserAnalytics {
    export type RequestParams = {};
    export type RequestQuery = UserAnalyticsQueryDto;
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = UserAnalyticsResponseDto;
  }
}
