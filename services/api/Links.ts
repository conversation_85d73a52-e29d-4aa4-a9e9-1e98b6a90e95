import { ContentType, HttpClient, RequestParams } from "./http-client";
import {
  UserLinkResponseDto,
  PaginationResponseDto,
  UserLinksQueryDto,
  UserAnalyticsResponseDto,
  UserAnalyticsQueryDto,
} from "./data-contracts";

export class Links<SecurityDataType = unknown> extends HttpClient<SecurityDataType> {
  /**
   * Get all user links
   *
   * @tags Links
   * @name LinksControllerGetUserLinks
   * @request GET:/links
   * @secure
   * @response `200` `PaginationResponseDto<UserLinkResponseDto>`
   * @response `401` `void` Unauthorized
   */
  linksControllerGetUserLinks = (
    query?: UserLinksQueryDto,
    params: RequestParams = {}
  ) =>
    this.request<PaginationResponseDto<UserLinkResponseDto>, void>({
      path: `/links`,
      method: "GET",
      query: query,
      secure: true,
      format: "json",
      ...params,
    });

  /**
   * Generate a new link
   *
   * @tags Links
   * @name LinksControllerGenerateLink
   * @request POST:/links/generate
   * @secure
   * @response `201` `{ shortUrl: string }`
   * @response `401` `void` Unauthorized
   */
  linksControllerGenerateLink = (
    data: { url: string },
    params: RequestParams = {}
  ) =>
    this.request<{ shortUrl: string }, void>({
      path: `/links/generate`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.Json,
      ...params,
    });

  /**
   * Get user analytics
   *
   * @tags Links
   * @name LinksControllerGetUserAnalytics
   * @request GET:/links/analytics
   * @secure
   * @response `200` `UserAnalyticsResponseDto`
   * @response `401` `void` Unauthorized
   */
  linksControllerGetUserAnalytics = (
    query?: UserAnalyticsQueryDto,
    params: RequestParams = {}
  ) =>
    this.request<UserAnalyticsResponseDto, void>({
      path: `/links/analytics`,
      method: "GET",
      query: query,
      secure: true,
      format: "json",
      ...params,
    });
}
