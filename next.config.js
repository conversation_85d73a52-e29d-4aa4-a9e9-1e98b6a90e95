//eslint-disable-next-line
const withPWA = require('@ducanh2912/next-pwa').default({
  dest: 'public',
});
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;
/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    unoptimized: false,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
      {
        protocol: 'http',
        hostname: '**',
      },
    ],
  },
  output: 'standalone',
  async rewrites() {
    return [
      {
        source: '/api/proxy/:path*',
        destination: `${BASE_URL}/:path*`,
        // Add this to ensure cookies are properly forwarded
        basePath: false,
      },
    ];
  },

  async redirects() {
    return [
      {
        source: '/store',
        destination: '/online-free-shopping-stores',
        permanent: true,
      },

      {
        source: '/mobile/:path*',
        destination: '/:path*',
        permanent: true,
      },
    ];
  },
};

module.exports = withPWA(nextConfig);
