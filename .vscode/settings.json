{"editor.formatOnSave": true, "editor defaultFormatter": "esbenp-prettier-vscode", "git.enableSmartcommit": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source. sortMembers": "explicit", "source.fixAll.eslint": "explicit", "source.organizeImports": "never"}, "cSpell.enabled": true, "cSpell.logLevel": "Debug", "editor.tabSize": 2, "cSpell.words": ["apdex", "camelcase", "cashbacks", "CLOUDINARY", "clsx", "destr", "<PERSON><PERSON><PERSON><PERSON>", "Giftcard", "giftcards", "mailgun", "Subdocument"], "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnType": false, "editor.formatOnPaste": true, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}