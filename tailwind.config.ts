import type { Config } from 'tailwindcss';

const config: Config = {
  darkMode: 'class',
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      screens: {
        sm: '390px',
      },
      fontFamily: {
        nexa: ['var(--font-nexa)'],
        pat: ['var(--font-patuaOne)'],
        pop: ['var(--font-poppins)'],
      },
      colors: {
        primary: 'var(--primary)',
        primaryDark: 'var(--primaryDark)',
        body: 'var(--body)',
        container: 'var(--container)',
        content: 'var(--content)',
        trendOffer: 'var(--trendOffer)',
        heading: 'var(--heading)',
        mainCard: 'var(--mainCard)',
        blackWhite: 'var(--blackWhite)',
        blackWhite2: 'var(--blackWhite2)',
      },
      animation: {
        fadeIn: 'fadeIn 0.3s ease-in-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
      },
    },
  },
  plugins: [],
};
export default config;
