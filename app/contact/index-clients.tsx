'use client';
import React, { useState, useRef } from 'react';
import CommonHeader from '../components/headers/common-header';
import BreadcrumbSaveShare from '../components/atoms/breadcrumb-container';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import emailjs from '@emailjs/browser';
import MailSVG from '../components/svg/mail';
import clsx from 'clsx';
import { protectEmail, displayProtectedEmail } from '@/utils/email-protection';
import SmartLink from '../components/common/smart-link';
import { LinkType } from '@/utils/link-utils';
import { PUBLIC_KEY, SERVICE_ID, TEMPLATE_ID } from '@/config';

// Define the form data interface
interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
}

const IndexClientsContact = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const formRef = useRef<HTMLFormElement>(null);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<ContactFormData>();

  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true);

    try {
      // Replace these with your actual EmailJS service ID, template ID, and public key
      const serviceId = SERVICE_ID;
      const templateId = TEMPLATE_ID;
      const publicKey = PUBLIC_KEY;

      // Send the email using EmailJS
      await emailjs.send(
        serviceId,
        templateId,
        {
          name: data.name,
          email: data.email,
          phone: data.phone,
          subject: data.subject,
          message: data.message,
        },
        publicKey
      );

      // Show success message
      toast.success('Your message has been sent successfully!');
      setIsSubmitted(true);
      reset(); // Reset the form
    } catch (error) {
      console.error('Error sending email:', error);
      toast.error('Failed to send message. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <CommonHeader headline='Contact Us' />
      <section className='max-w-[1280px] min-[1280px]:mx-auto'>
        <BreadcrumbSaveShare
          breadCrumbs={[
            { title: 'Cashback Home', link: '/' },
            { title: 'Contact Us' },
          ]}
        />

        <div className='mx-[6px] px-[8px] lg:mx-0 lg:px-[40px] py-[26px] bg-[#efefef] dark:bg-container'>
          {/* Hero Section */}
          <div className='text-center mb-8'>
            <div className='mx-auto w-[80px] h-[80px] lg:w-[120px] lg:h-[120px] relative mb-4'>
              <Image
                alt='Contact'
                className='mx-auto'
                fill
                priority
                src='/img/contact-hero.png'
              />
            </div>
            <h1 className='text-xl lg:text-2xl font-bold text-blackWhite mb-2'>
              Get in Touch
            </h1>
            <p className='text-sm lg:text-base text-gray-600 dark:text-gray-300 max-w-2xl mx-auto'>
              Have a question or need assistance? We're here to help! Fill out
              the form below and our team will get back to you as soon as
              possible.
            </p>
          </div>

          {/* Contact Form Section */}
          <div className='max-w-2xl mx-auto bg-white dark:bg-[#35383E] rounded-lg shadow-sm p-6 lg:p-8'>
            {isSubmitted ? (
              <motion.div
                animate={{ opacity: 1 }}
                className='text-center py-8'
                initial={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className='w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4'>
                  <MailSVG className='w-8 h-8 text-green-600 dark:text-green-300' />
                </div>
                <h3 className='text-lg font-medium text-gray-700 dark:text-gray-300 mb-2'>
                  Thank You for Contacting Us!
                </h3>
                <p className='text-gray-500 dark:text-gray-400 mb-6'>
                  We've received your message and will get back to you as soon
                  as possible.
                </p>
                <motion.button
                  className='bg-primary text-white px-6 py-3 rounded-md font-medium'
                  onClick={() => setIsSubmitted(false)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Send Another Message
                </motion.button>
              </motion.div>
            ) : (
              <form onSubmit={handleSubmit(onSubmit)} ref={formRef}>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-4'>
                  {/* Name Field */}
                  <div className='flex flex-col'>
                    <label
                      className='text-sm font-medium text-gray-700 dark:text-gray-300 mb-1'
                      htmlFor='name'
                    >
                      Full Name <span className='text-red-500'>*</span>
                    </label>
                    <input
                      className={clsx(
                        'px-4 py-2 border rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white',
                        errors.name
                          ? 'border-red-500'
                          : 'border-gray-300 dark:border-gray-600'
                      )}
                      id='name'
                      placeholder='Enter your full name'
                      type='text'
                      {...register('name', {
                        required: 'Name is required',
                        minLength: {
                          value: 3,
                          message: 'Name must be at least 3 characters',
                        },
                      })}
                    />
                    {errors.name && (
                      <span className='text-red-500 text-xs mt-1'>
                        {errors.name.message}
                      </span>
                    )}
                  </div>

                  {/* Email Field */}
                  <div className='flex flex-col'>
                    <label
                      className='text-sm font-medium text-gray-700 dark:text-gray-300 mb-1'
                      htmlFor='email'
                    >
                      Email Address <span className='text-red-500'>*</span>
                    </label>
                    <input
                      className={clsx(
                        'px-4 py-2 border rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white',
                        errors.email
                          ? 'border-red-500'
                          : 'border-gray-300 dark:border-gray-600'
                      )}
                      id='email'
                      placeholder='Enter your email address'
                      type='email'
                      {...register('email', {
                        required: 'Email is required',
                        pattern: {
                          value: /^[\w-]+@([\w-]+\.)+[\w-]{2,4}$/g,
                          message: 'Please enter a valid email address',
                        },
                      })}
                    />
                    {errors.email && (
                      <span className='text-red-500 text-xs mt-1'>
                        {errors.email.message}
                      </span>
                    )}
                  </div>

                  {/* Phone Field */}
                  <div className='flex flex-col'>
                    <label
                      className='text-sm font-medium text-gray-700 dark:text-gray-300 mb-1'
                      htmlFor='phone'
                    >
                      Phone Number
                    </label>
                    <input
                      className={clsx(
                        'px-4 py-2 border rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white',
                        errors.phone
                          ? 'border-red-500'
                          : 'border-gray-300 dark:border-gray-600'
                      )}
                      id='phone'
                      placeholder='Enter your phone number'
                      type='tel'
                      {...register('phone', {
                        pattern: {
                          value: /^[0-9]{10}$/g,
                          message: 'Please enter a valid 10-digit phone number',
                        },
                      })}
                    />
                    {errors.phone && (
                      <span className='text-red-500 text-xs mt-1'>
                        {errors.phone.message}
                      </span>
                    )}
                  </div>

                  {/* Subject Field */}
                  <div className='flex flex-col'>
                    <label
                      className='text-sm font-medium text-gray-700 dark:text-gray-300 mb-1'
                      htmlFor='subject'
                    >
                      Subject <span className='text-red-500'>*</span>
                    </label>
                    <input
                      className={clsx(
                        'px-4 py-2 border rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white',
                        errors.subject
                          ? 'border-red-500'
                          : 'border-gray-300 dark:border-gray-600'
                      )}
                      id='subject'
                      placeholder='Enter message subject'
                      type='text'
                      {...register('subject', {
                        required: 'Subject is required',
                      })}
                    />
                    {errors.subject && (
                      <span className='text-red-500 text-xs mt-1'>
                        {errors.subject.message}
                      </span>
                    )}
                  </div>
                </div>

                {/* Message Field */}
                <div className='flex flex-col mb-6'>
                  <label
                    className='text-sm font-medium text-gray-700 dark:text-gray-300 mb-1'
                    htmlFor='message'
                  >
                    Message <span className='text-red-500'>*</span>
                  </label>
                  <textarea
                    className={clsx(
                      'px-4 py-2 border rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white',
                      errors.message
                        ? 'border-red-500'
                        : 'border-gray-300 dark:border-gray-600'
                    )}
                    id='message'
                    placeholder='Type your message here...'
                    rows={5}
                    {...register('message', {
                      required: 'Message is required',
                      minLength: {
                        value: 20,
                        message: 'Message must be at least 20 characters',
                      },
                    })}
                  />
                  {errors.message && (
                    <span className='text-red-500 text-xs mt-1'>
                      {errors.message.message}
                    </span>
                  )}
                </div>

                {/* Submit Button */}
                <motion.button
                  className='w-full bg-primary text-white py-3 rounded-md font-medium flex items-center justify-center'
                  disabled={isSubmitting}
                  type='submit'
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {isSubmitting ? (
                    <>
                      <svg
                        className='animate-spin -ml-1 mr-3 h-5 w-5 text-white'
                        fill='none'
                        viewBox='0 0 24 24'
                        xmlns='http://www.w3.org/2000/svg'
                      >
                        <circle
                          className='opacity-25'
                          cx='12'
                          cy='12'
                          r='10'
                          stroke='currentColor'
                          strokeWidth='4'
                        />
                        <path
                          className='opacity-75'
                          d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                          fill='currentColor'
                        />
                      </svg>
                      Sending...
                    </>
                  ) : (
                    'Send Message'
                  )}
                </motion.button>
              </form>
            )}
          </div>

          {/* Contact Information Section */}
          <div className='max-w-2xl mx-auto mt-8 grid grid-cols-1 md:grid-cols-3 gap-4'>
            {/* Email */}
            <div className='bg-white dark:bg-[#35383E] rounded-lg p-4 text-center shadow-sm'>
              <div className='w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-3'>
                <MailSVG className='w-6 h-6 text-primary' />
              </div>
              <h3 className='text-sm font-semibold text-gray-800 dark:text-gray-200 mb-1'>
                Email Us
              </h3>
              <button
                className='text-xs text-primary hover:underline cursor-pointer border-none bg-transparent'
                onClick={protectEmail('<EMAIL>')}
                type='button'
              >
                {displayProtectedEmail('<EMAIL>')}
              </button>
            </div>

            {/* Phone */}
            <div className='bg-white dark:bg-[#35383E] rounded-lg p-4 text-center shadow-sm'>
              <div className='w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-3'>
                <MailSVG className='w-8 h-8 text-green-600 dark:text-green-300' />
              </div>
              <h3 className='text-sm font-semibold text-gray-800 dark:text-gray-200 mb-1'>
                Raise Ticket
              </h3>
              <p className='text-xs text-gray-600 dark:text-gray-400'>
                Tell us your concern
              </p>
              <SmartLink
                className='text-xs text-primary hover:underline'
                href='https://indiancashbackcom.tawk.help/'
                linkType={LinkType.EXTERNAL}
                target='_blank'
              >
                Start Chat
              </SmartLink>
            </div>

            {/* Live Chat */}
            <div className='bg-white dark:bg-[#35383E] rounded-lg p-4 text-center shadow-sm'>
              <div className='w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-3'>
                <svg
                  className='w-6 h-6 text-primary'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <path
                    d='M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z'
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                  />
                </svg>
              </div>
              <h3 className='text-sm font-semibold text-gray-800 dark:text-gray-200 mb-1'>
                Live Chat
              </h3>
              <p className='text-xs text-gray-600 dark:text-gray-400 mb-2'>
                Chat with our support team
              </p>
              <SmartLink
                className='text-xs text-primary hover:underline'
                href='https://tawk.to/indiancashback'
                linkType={LinkType.EXTERNAL}
                target='_blank'
              >
                Start Chat
              </SmartLink>
            </div>
          </div>

          {/* FAQ Section */}
          <div className='mt-12 bg-white dark:bg-[#35383E] rounded-lg p-6 text-center max-w-3xl mx-auto'>
            <h3 className='text-lg font-semibold text-blackWhite mb-2'>
              Frequently Asked Questions
            </h3>
            <p className='text-gray-600 dark:text-gray-300 mb-4'>
              Find quick answers to common questions in our FAQ section.
            </p>
            <motion.a
              className='inline-block bg-primary text-white px-6 py-3 rounded-md font-medium'
              href='/faqs'
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              View FAQs
            </motion.a>
          </div>
        </div>
      </section>
    </>
  );
};

export default IndexClientsContact;
