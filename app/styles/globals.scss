@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

@layer base {
  :root {
    --primary: #7366d9;
    --primaryDark: #5448b0;
    --content: #000;
    --heading: #1a1a1f;
    --body: #e7e9eb;
    --container: #f5f5f5;
    --trendOffer: #fdfdfe;
    --mainCard: #fff;
    --blackWhite: #000;
    --blackWhite2: #06020e;
  }
  button,
  [type='button'],
  [type='reset'],
  [type='submit'] {
    background-color: unset;
  }
}
.dark {
  --content: #d2d6db;
  --heading: #d2d6db;
  --body: #18191d;
  --container: #202327;
  --mainCard: #2d2e32;
  --blackWhite: #fff;
  --blackWhite2: #e7e9eb;
}

@layer utilities {
  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /* Hide default scrollbar for Chrome, Safari and Opera */
  .scrollbarNone::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbarNone {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Modern Custom Scrollbar */
  .customScrollbar {
    /* For Firefox */
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.05) transparent;
    .dark & {
      scrollbar-color: rgba(255, 255, 255, 0.05) transparent;
    }

    /* For Chrome, Safari, and Opera */
    &::-webkit-scrollbar {
      width: 6px; /* vertical scrollbar width */
      height: 6px; /* horizontal scrollbar height */

      @media screen and (min-width: 1024px) {
        width: 8px;
        height: 8px;
      }
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 10px;
      margin: 4px;

      .dark & {
        background: rgba(255, 255, 255, 0.05);
      }
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(45deg, #c1b8ff, #aea3ff);
      border-radius: 10px;
      border: 2px solid transparent;
      background-clip: content-box;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(45deg, #aea3ff, #c1b8ff);
        border-width: 1px;
      }

      &:active {
        background: #aea3ff;
      }
    }

    /* For horizontal scrollbar */
    &::-webkit-scrollbar-thumb:horizontal {
      border-width: 2px 4px;
    }

    /* Corners where vertical and horizontal scrollbars meet */
    &::-webkit-scrollbar-corner {
      background: transparent;
    }
  }
}

/* Global scrollbar styles for the entire application */
html {
  /* For Firefox */
  scrollbar-width: thin;
  scrollbar-color: #4747479d transparent;

  /* For Chrome, Safari, and Opera */
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);

    .dark & {
      background: rgba(255, 255, 255, 0.05);
    }
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #c1b8ff, #aea3ff);
    border-radius: 10px;
    border: 2px solid transparent;
    background-clip: content-box;
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(45deg, #aea3ff, #c1b8ff);
      border-width: 1px;
    }

    &:active {
      background: #aea3ff;
    }
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }
}

body {
  color: rgb(var(--content));
  background: var(--body);
}

header .mainHeader {
  background: linear-gradient(to right, #7264db, #4335bd);
}

.curvedHeader {
  background: linear-gradient(to right, #7264db, #4335bd);
  position: relative;
  height: 75px;
  margin-top: -2px;
  border-bottom-left-radius: 50% 15%;
  border-bottom-right-radius: 50% 15%;
}

//short the height of left/right navigation divs of react-insta-stories
.story > div > div:nth-child(3) > div {
  height: 80%;
}

.glassmorphism {
  background: rgba(25, 23, 23, 0.25);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.storyGradient {
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 0.87%,
    rgba(0, 0, 0, 0.8) 74.88%
  );
}

.heroCoverSwiper,
.testimonialCoverSwiper {
  .swiper-slide-shadow-left,
  .swiper-slide-shadow-left {
    background-image: none !important;
  }
  .swiper-slide {
    width: 298px;
    height: 172px;
    box-shadow: 0px 4px 9px 0px rgba(0, 0, 0, 0.1);
    filter: blur(2px);
    border-radius: 10px;
    position: relative;

    @media screen and (min-width: 768px) {
      width: 450px;
      height: 260px;
    }

    .hero_swiperImg {
      position: absolute;
      object-fit: cover;
      border-radius: 10px;
      height: 100%;
      width: 100%;
    }
  }
  .swiper-slide-active {
    filter: none;
  }
  .swiper-pagination {
    bottom: 5px !important;

    .swiper-pagination-bullet {
      border: 1px solid white;
      opacity: 0.8 !important;
    }

    .swiper-pagination-bullet-active {
      background-color: var(--primary);
    }
  }
}

.desktopHeroSwiper {
  .swiper-pagination-bullet {
    background: white;
    opacity: 0.29;
  }
}

.missedOffersWrapper {
  background: linear-gradient(270deg, #5430a0 40.93%, #a174ff 128.68%);

  @media screen and (min-width: 1024px) {
    background: #f5f5f5;
  }
}

.testimonialCoverSwiper {
  .swiper-slide {
    width: 238px;

    @media screen and (min-width: 768px) {
      width: 450px;
      height: auto;
      min-height: 200px;
    }
    height: auto;
    filter: none;
  }
}

.quickAccessSwiper {
  .swiper-pagination {
    top: 85px !important;
  }
}

.bottomNavbarGradient {
  background: linear-gradient(180deg, #fff 0%, #efefef 100%);
  box-shadow: 5px 0px 6px 0px rgba(0, 0, 0, 0.04);
}
//for dark mode
.dark {
  .bottomNavbarGradient {
    background: linear-gradient(180deg, #1d1e25 35.21%, #16171c 100%);
    box-shadow: 5px 0px 6px 0px rgba(0, 0, 0, 0.04);
  }
}

// -------------sliding button design-----------------
.slidingBtnContainer {
  width: min-content;
  margin: auto;

  .slidingBtnTabs {
    position: relative;
    display: flex;
    background: #ececec;
    border-radius: 25px;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);

    label {
      font-size: 12px;
      // font-family: var(--font-nexa);
      font-weight: 600;
      outline: none;
      color: var(--content);
    }

    input[type='radio'] {
      display: none;
    }

    .slidingBtnTab {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 35px;
      width: 90px;
      border-radius: 25px;
      cursor: pointer;
      transition: color 0.15s ease-in;
    }

    input[type='radio']:checked + label {
      color: white;
    }

    input[id^='radio1']:checked ~ .glider {
      transform: translateX(0);
    }

    input[id^='radio2']:checked ~ .glider {
      transform: translateX(100%);
    }

    input[id^='radio3']:checked ~ .glider {
      transform: translateX(200%);
    }

    input[id^='radio4']:checked ~ .glider {
      transform: translateX(300%);
    }
    input[id^='radio5']:checked ~ .glider {
      transform: translateX(400%);
    }

    .glider {
      position: absolute;
      display: flex;
      height: 35px;
      width: 90px;
      background-color: #7366d9;
      color: white;
      z-index: 1;
      border-radius: 25px;
      transition: 0.25s ease-out;
      box-shadow: 3px 0px 0px 0px rgba(0, 0, 0, 0.05);
    }
  }

  .slidingBtnTabs * {
    z-index: 2;
  }
}
// --------------------------------------------------

.disc {
  ul {
    list-style: inside;
    list-style-type: disc;
  }
}

img,
a {
  user-select: none;
  -webkit-user-drag: none;
}

.globalSearchWrapper {
  ::placeholder {
    color: #8d8d8d;
    font-weight: 400;
  }
}

// --------------------------box shadows-----------------------
.shadowGloblaSearchResult {
  @media screen and (min-width: 1024px) {
    box-shadow: 0px 4px 23px 0px rgba(0, 0, 0, 0.46);
  }
}
//---------------------------AntD library---------------------------------
.dark {
  .ant-breadcrumb-separator {
    color: white;
  }
  .ant-breadcrumb a:hover {
    background-color: black;
  }
  .ant-picker-dropdown .ant-picker-panel-container {
    background: #515662 !important;
  }
  .ant-picker .ant-picker-clear {
    background: #515662 !important;
  }
  .ant-picker .ant-picker-input-placeholder > input {
    color: white !important;
  }
}
.ant-picker .ant-picker-input-placeholder > input {
  color: black !important;
}

.ant-breadcrumb ol {
  display: flex !important;
  .ant-breadcrumb-link a {
    height: fit-content;
  }
}

.ant-radio {
  border: 1px solid #1677ff !important;
}

.ant-upload-list-item {
  background-color: rgba(0, 0, 0, 0.04);
}

.mainCardMenu {
  .ant-dropdown-menu-item:not(:first-child) {
    margin-top: 10px !important;
  }
}
// -------------------------------------------------------------

.maxLines2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* number of lines to show */
  line-clamp: 2;
  -webkit-box-orient: vertical;
  -ms-box-orient: vertical;
}

.maxLines3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* number of lines to show */
  line-clamp: 3;
  -webkit-box-orient: vertical;
  -ms-box-orient: vertical;
}

.swiper-button-disabled {
  opacity: 0.3;
}

@media screen and (max-width: 768px) {
  .transDateRangeDropdown {
    left: 50% !important;
    transform: translateX(-50%) !important;
  }
}

.user-subscription {
  .ant-switch {
    .ant-switch-handle::before {
      background-color: #838383;
    }

    &.ant-switch-checked {
      .ant-switch-handle::before {
        background-color: var(--primary);
      }
    }
  }
}

.datePickerModal {
  .ant-btn-primary {
    background-color: var(--primary) !important;
  }

  .ant-modal-content {
    border-radius: 10px;
    overflow: hidden;
  }

  .ant-modal-header {
    padding: 12px 16px;
  }

  .ant-modal-body {
    padding: 16px;
  }

  .ant-modal-footer {
    padding: 10px 16px;
    margin-top: 0;
  }

  .ant-picker {
    width: 100%;
    border-radius: 6px;
    border: 1px solid #e2e2e2;
    transition: all 0.3s ease;

    &:hover,
    &:focus {
      border-color: var(--primary);
    }
  }

  .dark & {
    .ant-picker {
      background-color: #3d4049;
      border-color: #515662;
      color: white;

      input {
        color: white;
      }
    }
  }
}

.ant-select-auto-complete {
  .ant-select-selector {
    border: 1px solid var(--primary) !important;
    font-size: 10px;

    @media screen and (min-width: 1024px) {
      font-size: 12px;
    }
  }
}

.dark {
  .ant-select-selector {
    color: white !important;
    background: var(--body) !important;
  }
}

.autocomplete-disabled {
  .ant-select-selector {
    background: #dddddd !important;
    border: none !important;
  }
}

.dark {
  .autocomplete-disabled {
    .ant-select-selector {
      background: #797e90 !important;
    }
  }
}

.datePickerModal {
  .ant-modal-title {
    font-size: 10px;
  }

  @media screen and (min-width: 1024px) {
    .ant-modal-title {
      font-size: 12px;
    }
  }
}

input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button,
input[type='number'] {
  -webkit-appearance: none;
  margin: 1;
  -moz-appearance: textfield !important;
  appearance: none;
}

// --------------highCharts---------------------
.highcharts-root {
  @media screen and (min-width: 1024px) {
    font-size: 14px !important;
  }
  @media screen and (min-width: 1280px) {
    font-size: 16px !important;
  }
  @media screen and (min-width: 1280px) {
    font-size: 18px !important;
  }
}

@keyframes shimmerWave {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer-wave {
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.06) 25%,
    rgba(0, 0, 0, 0.15) 37%,
    rgba(0, 0, 0, 0.06) 63%
  );
  background-size: 200px 100%;
  animation: shimmerWave 1.5s infinite linear;
}

.dark .shimmer-wave {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.06) 25%,
    rgba(255, 255, 255, 0.15) 37%,
    rgba(255, 255, 255, 0.06) 63%
  );
  background-size: 200px 100%;
  animation: shimmerWave 1.5s infinite linear;
}

.header-container {
  @apply w-full lg:px-12 2xl:px-44;
}

// Coin loader animation
.coin-loader {
  position: relative;
  width: 24px;
  height: 24px;
}

.coin-loader:after {
  content: '₹';
  position: absolute;
  display: block;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  text-align: center;
  line-height: 20px;
  font-size: 16px;
  font-weight: bold;
  background: #ffd700;
  color: #daa520;
  border: 2px double;
  box-sizing: border-box;
  box-shadow: 1px 1px 1px 0.5px rgba(0, 0, 0, 0.1);
  animation: coinFlip 4s cubic-bezier(0, 0.2, 0.8, 1) infinite;
  transform-style: preserve-3d;
}

@keyframes coinFlip {
  0%,
  100% {
    animation-timing-function: cubic-bezier(0.5, 0, 1, 0.5);
  }

  0% {
    transform: rotateY(0deg);
  }

  50% {
    transform: rotateY(1800deg);
    animation-timing-function: cubic-bezier(0, 0.5, 0.5, 1);
  }

  100% {
    transform: rotateY(3600deg);
  }
}
