'use client';
import Image from 'next/image';
import card1 from '@/public/svg/footer-cards/card1.svg';
import card2 from '@/public/svg/footer-cards/card2.svg';
import card3 from '@/public/svg/footer-cards/card3.svg';
import card4 from '@/public/svg/footer-cards/card4.svg';
import card5 from '@/public/svg/footer-cards/card5.svg';
import card6 from '@/public/svg/footer-cards/card6.svg';
import card7 from '@/public/svg/footer-cards/card7.svg';
import card8 from '@/public/svg/footer-cards/card8.svg';
import card9 from '@/public/svg/footer-cards/card9.svg';
import card10 from '@/public/svg/footer-cards/card10.svg';
import TelegramSVG from '../svg/social/telegram';
import TwitterSVG from '../svg/social/twitter';
import InstagramSVG from '../svg/social/instagram';
import FacebookSVG from '../svg/social/facebook';
import YoutubeSVG from '../svg/social/youtube';
import clsx from 'clsx';
import { usePathname } from 'next/navigation';
import ICBLogo from '../svg/icb-logo';
import SmartLink from '../common/smart-link';
import { LinkType } from '@/utils/link-utils';
import { protectEmail, displayProtectedEmail } from '@/utils/email-protection';
import WhatsappSVG from '../svg/social/whatsapp';

function MainFooter({ rootClass }: { rootClass?: string }) {
  const pathname = usePathname();
  if (pathname === '/redirecting') {
    return;
  }
  return (
    <footer
      className={clsx(
        rootClass,
        pathname === '/' && 'mt-5',
        'bg-[#7366D9] w-full relative flex flex-col justify-around h-full mx-auto mb-[60px] lg:mb-0 pt-[40px]'
      )}
    >
      <div className='grid lg:grid-cols-10 grid-cols-3 lg:px-[100px] px-[16px] pb-[40px] gap-y-6 md:gap-y-0 gap-x-3'>
        {/* ICB Logo and Description */}
        <div className='col-span-3 lg:col-span-3 flex flex-col items-start mb-6 lg:mb-0'>
          <div className='flex items-center mb-4'>
            <ICBLogo className='text-white w-[40px] h-[30px] mr-2' />
            <div className='flex flex-col'>
              <span className='text-white font-nexa text-sm font-bold'>
                Indian CashBack
              </span>
            </div>
          </div>
          <h1 className='text-[#CDC7FF] text-xs leading-relaxed mb-4 max-w-xl lg:max-w-[280px] font-bold'>
            India's leading cashback and coupons website.
          </h1>
          <p className='text-[#CDC7FF] text-xs leading-relaxed mb-4 max-w-xl lg:max-w-[280px]'>
            IndianCashBack is India's leading cashback and coupons website. We
            help you save on all your online shopping with the best offers and
            deals from top brands and stores.
          </p>
          {/* <div className='flex items-center gap-2 bg-gradient-to-r from-[#8171FF] to-[#574ABE] px-3 py-1.5 rounded-full shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 cursor-pointer'>
            <span className='text-white text-xs font-medium'>Download our app</span>
            <RightArrow className='text-white w-[8px]' />
          </div> */}
          {/* Android App Coming Soon Banner */}
          <div className='w-auto flex justify-center items-center py-0 bg-transparent'>
            <SmartLink className='group' href='#' linkType={LinkType.INTERNAL}>
              <div className='flex items-center gap-2 bg-gradient-to-r from-[#8171FF] to-[#574ABE] px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 group-hover:from-[#574ABE] group-hover:to-[#8171FF]'>
                <div className='relative flex items-center justify-center w-[24px] h-[24px] bg-white rounded-full animate-pulse'>
                  {/* <Image
                  alt='android-icon'
                  className='w-[16px] h-[16px] text-[#574ABE]'
                  height={16}
                  src='/svg/android-icon.svg'
                  width={16}
                /> */}
                  🎉
                </div>
                <span className='text-white text-xs font-medium whitespace-nowrap'>
                  Android App Coming Soon!
                </span>
                {/* <div className='hidden lg:flex items-center'>
                <span className='text-[#FFC554] text-xs ml-2 font-bold'>🎉 Get notified</span>
              </div>
              <div className='hidden lg:flex items-center ml-2 animate-bounce'>
                <RightArrow className='text-white w-[10px]' />
              </div> */}
              </div>
            </SmartLink>
          </div>
        </div>

        <div className='col-span-3 lg:col-span-4 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-3 lg:gap-6'>
          <div className='text-white'>
            <p className='py-3 text-xs md:text-sm lg:text-base font-medium'>
              Knowledge Base
            </p>
            <div className='font-light text-[#CDC7FF] text-xs flex-col flex gap-3'>
              <SmartLink
                className='hover:text-white transition-colors duration-200 hover:underline'
                href='https://indiancashbackcom.tawk.help/'
                linkType={LinkType.EXTERNAL}
                target='_blank'
              >
                My Tickets
              </SmartLink>
              <SmartLink
                className='hover:text-white transition-colors duration-200 hover:underline'
                href='https://tawk.to/indiancashback'
                linkType={LinkType.EXTERNAL}
                target='_blank'
              >
                Support
              </SmartLink>
              <SmartLink
                className='hover:text-white transition-colors duration-200 hover:underline'
                href='/faqs'
                linkType={LinkType.INTERNAL}
                target='_blank'
              >
                FAQ
              </SmartLink>
              <SmartLink
                className='hover:text-white transition-colors duration-200 hover:underline'
                href='https://blog.indiancashback.com/en'
                linkType={LinkType.EXTERNAL}
                target='_blank'
              >
                Blog
              </SmartLink>
              <SmartLink
                className='hover:text-white transition-colors duration-200 hover:underline'
                href='/about-us'
                linkType={LinkType.EXTERNAL}
                target='_blank'
              >
                About ICB
              </SmartLink>
            </div>
          </div>
          <div className='text-white'>
            <p className='py-3 text-xs md:text-sm lg:text-base font-medium'>
              Help
            </p>
            <div className='font-light text-[#CDC7FF] text-xs flex-col flex gap-3'>
              <SmartLink
                className='hover:text-white transition-colors duration-200 hover:underline'
                href='/contact'
                linkType={LinkType.INTERNAL}
              >
                Contact Us
              </SmartLink>
              <SmartLink
                className='hover:text-white transition-colors duration-200 hover:underline'
                href='https://indiancashbackcom.tawk.help/article/understanding-how-indiancashback-works'
                linkType={LinkType.EXTERNAL}
              >
                How ICB Works?
              </SmartLink>
              <SmartLink
                className='hover:text-white transition-colors duration-200 hover:underline'
                href='/best-practices'
                linkType={LinkType.EXTERNAL}
                target='_blank'
              >
                CashBack - Best Practices
              </SmartLink>
            </div>
          </div>
          <div className='text-white hidden md:block'>
            <p className='py-3 text-xs md:text-sm lg:text-base font-medium'>
              Policy
            </p>
            <div className='font-light text-[#CDC7FF] text-xs flex-col flex gap-3'>
              <SmartLink
                className='hover:text-white transition-colors duration-200 hover:underline'
                href='/terms-and-conditions'
                linkType={LinkType.INTERNAL}
              >
                Terms and Conditions
              </SmartLink>
              <SmartLink
                className='hover:text-white transition-colors duration-200 hover:underline'
                href='/privacy-policies'
                linkType={LinkType.INTERNAL}
              >
                Privacy Policy
              </SmartLink>
            </div>
            <SmartLink
              className='pull-left footer_app_link play'
              href='https://www.google.com/transparencyreport/safebrowsing/diagnostic/#url=https://www.indiancashback.com'
              linkType={LinkType.EXTERNAL}
              target='_blank'
            >
              <p className='py-3 text-xs md:text-sm lg:text-base font-medium'>
                Security
              </p>
              <Image
                alt='google safe browsing'
                className='rounded-md'
                height='42'
                src='/img/safe-browse.png'
                width='116'
              />
            </SmartLink>
          </div>
        </div>
        <div className='col-span-3 lg:col-span-3 grid grid-cols-1 pt-[10px] md:pt-0'>
          <SmartLink
            className='text-white md:hidden'
            href='https://www.google.com/transparencyreport/safebrowsing/diagnostic/#url=https://www.indiancashback.com'
            linkType={LinkType.EXTERNAL}
            target='_blank'
          >
            <p className='py-3 text-xs md:text-sm lg:text-base font-medium'>
              Security
            </p>
            <Image
              alt='google safe browsing'
              className='rounded-md'
              height='42'
              src='/img/safe-browse.png'
              width='116'
            />
          </SmartLink>
          <div className='text-white md:hidden'>
            <p className='py-3 text-xs md:text-sm lg:text-base font-medium'>
              Policy
            </p>
            <div className='font-light text-[#CDC7FF] text-xs flex-col flex gap-3'>
              <SmartLink
                className='hover:text-white transition-colors duration-200 hover:underline'
                href='/terms-and-conditions'
                linkType={LinkType.INTERNAL}
              >
                Terms and Conditions
              </SmartLink>
              <SmartLink
                className='hover:text-white transition-colors duration-200 hover:underline'
                href='/privacy-policies'
                linkType={LinkType.EXTERNAL}
              >
                Privacy Policy
              </SmartLink>
            </div>
          </div>
          <div className='col-span-2 lg:col-span-1 mt-4 lg:mt-0'>
            <p className='py-3 text-white text-xs md:text-sm lg:text-base font-medium'>
              Office Address
            </p>
            <div className='font-light text-[#CDC7FF] text-xs flex-col flex gap-3'>
              <p className='text tracking-widest leading-[1.6]'>
                Revontulet solutions private limited <br />
                No.112, AKR Tech Park, <br />
                "A" Block, 7th Mile Hosur Rd, Krishna Reddy Industrial Area,{' '}
                <br />
                Bengaluru - 560068
              </p>
              <SmartLink
                className='hover:text-white transition-colors duration-200 hover:underline'
                href='#'
                linkType={LinkType.EXTERNAL}
                onClick={protectEmail('<EMAIL>')}
              >
                {displayProtectedEmail('<EMAIL>')}
              </SmartLink>
            </div>
          </div>
        </div>
      </div>

      <div className='w-full flex flex-col-reverse lg:flex-row justify-around items-center'>
        <div className='flex flex-col lg:flex-row justify-center items-center gap-4 lg:gap-6 bg-[#4B3FA9] w-full py-4 lg:py-0 lg:h-[80px] lg:w-[40%]'>
          <p className='text-white text-[10px] md:text-xs tracking-widest'>
            Follow Us
          </p>
          <div className='flex items-center gap-4 cursor-pointer'>
            <SmartLink
              className='hover:opacity-80 transition-opacity transform hover:scale-110 duration-200'
              href='https://t.me/indiancashbackofficial'
              linkType={LinkType.EXTERNAL}
              target='_blank'
            >
              <TelegramSVG className='text-white w-[26px]' />
            </SmartLink>
            <SmartLink
              href='https://whatsapp.com/channel/0029VaLW0IoCcW4zFzzcqY23'
              linkType={LinkType.EXTERNAL}
              target='_blank'
            >
              <WhatsappSVG className='text-white w-[26px]' />
            </SmartLink>
            <SmartLink
              className='hover:opacity-80 transition-opacity transform hover:scale-110 duration-200'
              href='https://twitter.com/Indian_cashback'
              linkType={LinkType.EXTERNAL}
              target='_blank'
            >
              <TwitterSVG className='text-white w-[26px]' />
            </SmartLink>
            <SmartLink
              className='hover:opacity-80 transition-opacity transform hover:scale-110 duration-200'
              href='https://www.instagram.com/indian_cashback/'
              linkType={LinkType.EXTERNAL}
              target='_blank'
            >
              <InstagramSVG className='text-white w-[26px]' />
            </SmartLink>
            <SmartLink
              className='hover:opacity-80 transition-opacity transform hover:scale-110 duration-200'
              href='https://www.facebook.com/indiancashback'
              linkType={LinkType.EXTERNAL}
              target='_blank'
            >
              <FacebookSVG className='text-white w-[26px]' />
            </SmartLink>
            <SmartLink
              className='hover:opacity-80 transition-opacity transform hover:scale-110 duration-200'
              href='https://www.youtube.com/indiancashback'
              linkType={LinkType.EXTERNAL}
              target='_blank'
            >
              <YoutubeSVG className='text-white w-[28px]' />
            </SmartLink>
          </div>
        </div>

        <div className='flex justify-center flex-wrap items-center gap-1 md:gap-2 py-6 lg:py-0 lg:h-[80px] lg:bg-[#4B3FA9] flex-grow'>
          <Image
            alt='card-svg'
            className='hover:scale-110 transition-transform duration-200 w-[33px] md:w-[40px] lg:w-[47px]'
            src={card1}
          />
          <Image
            alt='card-svg'
            className='hover:scale-110 transition-transform duration-200 w-[33px] md:w-[40px] lg:w-[47px]'
            src={card2}
          />
          <Image
            alt='card-svg'
            className='hover:scale-110 transition-transform duration-200 w-[33px] md:w-[40px] lg:w-[47px]'
            src={card3}
          />
          <Image
            alt='card-svg'
            className='hover:scale-110 transition-transform duration-200 w-[33px] md:w-[40px] lg:w-[47px]'
            src={card4}
          />
          <Image
            alt='card-svg'
            className='hover:scale-110 transition-transform duration-200 w-[33px] md:w-[40px] lg:w-[47px]'
            src={card5}
          />
          <Image
            alt='card-svg'
            className='hover:scale-110 transition-transform duration-200 w-[33px] md:w-[40px] lg:w-[47px]'
            src={card6}
          />
          <Image
            alt='card-svg'
            className='hover:scale-110 transition-transform duration-200 w-[33px] md:w-[40px] lg:w-[47px]'
            src={card7}
          />
          <Image
            alt='card-svg'
            className='hover:scale-110 transition-transform duration-200 w-[33px] md:w-[40px] lg:w-[47px]'
            src={card8}
          />
          <Image
            alt='card-svg'
            className='hover:scale-110 transition-transform duration-200 w-[33px] md:w-[40px] lg:w-[47px]'
            src={card9}
          />
          <Image
            alt='card-svg'
            className='hover:scale-110 transition-transform duration-200 w-[33px] md:w-[40px] lg:w-[47px]'
            src={card10}
          />
        </div>
      </div>
      <div className='text-center w-full text-white text-[10px] md:text-xs tracking-widest flex items-center justify-center py-4 bg-[#4B3FA9] lg:bg-transparent lg:h-[60px]'>
        <p>
          2013 - {new Date().getFullYear()} Indiancashback.com (
          <SmartLink
            href='https://revontulet.solutions/'
            linkType={LinkType.EXTERNAL}
            target='_blank'
          >
            REVONTULET SOLUTIONS
          </SmartLink>
          ). All rights reserved.
        </p>
      </div>
    </footer>
  );
}

export default MainFooter;
