'use client';

import { useAppDispatch } from '@/redux/hooks';
import { setIsUserLogin, setUserDetails } from '@/redux/slices/auth-slice';
import fetchWrapper from '@/utils/fetch-wrapper';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import type { GetUserProfileResponseItem } from '@/services/api/data-contracts';

/**
 * TokenVerifier component that checks for a token parameter in the URL
 * and verifies it with the backend
 */
const TokenVerifier = () => {
  const searchParams = useSearchParams();
  const dispatch = useAppDispatch();
  const router = useRouter();
  const pathname = usePathname();
  const [isVerifying, setIsVerifying] = useState(false);

  useEffect(() => {
    const verifyToken = async () => {
      // Get the token from URL query parameters
      const token = searchParams.get('token');

      // If no token is present or verification is already in progress, do nothing
      if (!token || isVerifying) return;

      // Set verifying state to true
      setIsVerifying(true);

      try {
        // Make API call to verify the token
        const response = await fetchWrapper<boolean>(
          `/api/proxy/auth/verify-token?token=${token}`,
          {
            method: 'GET',
            suppressToast: true, // Don't show error toasts for token verification
          }
        );

        // If token is valid
        if (response) {
          // Update Redux store to indicate user is logged in
          // Note: The server should have already set the authentication cookie
          dispatch(setIsUserLogin(true));

          try {
            // Fetch user profile data
            const userData = await fetchWrapper<GetUserProfileResponseItem>(
              '/api/proxy/users/me',
              {
                suppressToast: true, // Don't show error toasts for user data fetch
              }
            );

            // Update user details in Redux store
            dispatch(
              setUserDetails({
                ...userData,
                mobile: userData.mobile?.toString(),
                avatar:
                  userData.avatar ||
                  `https://ui-avatars.com/api/?name=${
                    userData?.name || 'John Doe'
                  }&background=random&rounded=true&format=png`,
              })
            );
          } catch (userDataError) {
            console.error('Failed to fetch user data:', userDataError);
            // Continue with authentication even if user data fetch fails
          }

          // Show success message
          toast.success('Successfully authenticated');

          // Handle redirects
          const redirectUrlValue = localStorage.getItem('redirectTo');
          // Get the redirect URL from localStorage or use current path
          const redirectUrl = redirectUrlValue || pathname;

          // Remove the token from the URL to prevent security issues
          const url = new URL(window.location.href);
          url.searchParams.delete('token');

          // If we're on a protected page or have a specific redirect URL, navigate there
          if (redirectUrl && redirectUrl !== '/' && redirectUrl !== pathname) {
            router.push(redirectUrl);
          } else {
            // Replace the current URL without the token parameter
            router.replace(url.pathname + url.search);
          }

          // Refresh the page to ensure all components are updated with the new auth state
          router.refresh();
        } else {
          // If token is invalid, show error message
          toast.error('Invalid or expired token');

          // Remove the token from the URL
          const url = new URL(window.location.href);
          url.searchParams.delete('token');
          router.replace(url.pathname + url.search);
        }
      } catch (error) {
        // Handle API call errors
        console.error('Token verification failed:', error);
        toast.error(
          'Failed to verify token. Please try again or log in manually.'
        );

        // Remove the token from the URL
        const url = new URL(window.location.href);
        url.searchParams.delete('token');
        router.replace(url.pathname + url.search);
      } finally {
        // Set verifying state to false
        setIsVerifying(false);
      }
    };

    // Call the verification function
    verifyToken();
  }, [searchParams, dispatch, router, pathname]);

  useEffect(() => {
    const referralCode = searchParams.get('r');
    console.log({ referralCode });
    if (referralCode) {
      //if referralCode exist in url then take from them and set into local storage
      localStorage.setItem('referralCode', `${referralCode}`);
    }
    // return () => {};
  }, [searchParams]);

  // This component doesn't render anything visible to the user
  // We're using the isVerifying state internally to track the verification process
  // and prevent multiple verification attempts if the component re-renders
  return null;
};

export default TokenVerifier;
