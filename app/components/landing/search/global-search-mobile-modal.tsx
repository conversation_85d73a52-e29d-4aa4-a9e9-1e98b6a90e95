'use client';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import {
  setSearchValue,
  setShowSearchLeftPanel,
  setShowSuggestions,
} from '@/redux/slices/global-search-slice';
import React, { useEffect, useRef, useState } from 'react';
import RightArrow from '../../svg/right-arrow';
import SearchSVG from '../../svg/search';
import CrossSVG from '../../svg/cross';
import GlobalSearchResult from './global-search-result';
import { Drawer } from 'antd';
import clsx from 'clsx';
import { Loader2 } from 'lucide-react';

const GlobalSearchMobileLeftPanel = () => {
  const globalSearchRef = useRef<HTMLInputElement>(null);
  const { searchValue, isShowSearchLeftPanel } =
    useAppSelector((state) => state.globalSearch);
  const [isLoading, setIsLoading] = useState(false);
  const [hasFocus, setHasFocus] = useState(false);
  const dispatch = useAppDispatch();
  
  useEffect(() => {
    if (globalSearchRef.current && isShowSearchLeftPanel) {
      globalSearchRef.current.focus();
    }
  }, [isShowSearchLeftPanel]);

  // Simulate loading state when typing
  useEffect(() => {
    if (searchValue && searchValue.length >= 2) {
      setIsLoading(true);
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 600);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [searchValue]);

  const handleClearSearch = () => {
    if (searchValue) {
      dispatch(setSearchValue(''));
      dispatch(setShowSuggestions(true));
      if (globalSearchRef.current) {
        globalSearchRef.current.focus();
      }
    }
  };

  const handleClose = () => {
    dispatch(setShowSearchLeftPanel(false));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      if (searchValue) {
        dispatch(setSearchValue(''));
      } else {
        dispatch(setShowSearchLeftPanel(false));
      }
    }
  };

  return (
    <Drawer
      className='font-pop'
      classNames={{ 
        body: 'bg-container !p-0 scrollbarNone',
        content: 'shadow-lg transition-transform duration-300'
      }}
      closable={false}
      destroyOnClose={false}
      height={'100vh'}
      maskClosable={true}
      onClose={handleClose}
      open={isShowSearchLeftPanel}
      placement={'right'}
      rootClassName='outline-none'
      styles={{
        mask: { background: 'rgba(0, 0, 0, 0.65)' },
        wrapper: { width: '100%' },
      }}
    >
      <div className='bg-body h-full'>
        <div className={clsx(
          'searchContainer w-full relative h-[51px] bg-[#F2F2F2] dark:bg-[#06020E] transition-all duration-300 px-3 flex items-center gap-3',
          hasFocus && 'border-b-2 border-primary/50'
        )}>
          <div 
            aria-label="Close search"
            className='inline-flex items-center justify-center p-2 rounded-full hover:bg-gray-300/70 dark:hover:bg-gray-800/70 active:bg-gray-400/70 dark:active:bg-gray-700/70 transition-colors cursor-pointer'
            onClick={handleClose}
            onKeyDown={(e) => e.key === 'Enter' && handleClose()}
            role="button"
            tabIndex={0}
          >
            <RightArrow
              className='rotate-[180deg] text-black dark:text-white w-[20px] h-[20px]'
            />
          </div>
          <input
            aria-label="Search"
            className='outline-none border-none grow h-full text-sm dark:text-white font-medium bg-transparent placeholder:text-gray-500 dark:placeholder:text-gray-400'
            onBlur={() => setHasFocus(false)}
            onChange={(e) => dispatch(setSearchValue(e.target.value))}
            onFocus={() => setHasFocus(true)}
            onKeyDown={handleKeyDown}
            placeholder='Search Product, coupon, deals, cashback...'
            ref={globalSearchRef}
            value={searchValue}
          />
          <div
            aria-label={searchValue ? "Clear search" : undefined}
            className={clsx(
              'inline-flex items-center justify-center p-2 rounded-full',
              searchValue && 'hover:bg-gray-300/70 dark:hover:bg-gray-800/70 active:bg-gray-400/70 dark:active:bg-gray-700/70 cursor-pointer',
              isLoading && 'pointer-events-none'
            )}
            onClick={handleClearSearch}
            onKeyDown={(e) => searchValue && e.key === 'Enter' && handleClearSearch()}
            role={searchValue ? "button" : undefined}
            tabIndex={searchValue ? 0 : -1}
          >
            {isLoading ? (
              <Loader2 className="text-black dark:text-white w-[20px] animate-spin" />
            ) : searchValue ? (
              <CrossSVG className='text-black dark:text-white w-[18px] hover:scale-110 transition-transform duration-200' />
            ) : (
              <SearchSVG className='text-black dark:text-white w-[20px]' />
            )}
          </div>
        </div>
        <div className='animate-fadeIn transition-all duration-300 overflow-auto max-h-[calc(100vh-51px)]'>
          <GlobalSearchResult />
        </div>
      </div>
    </Drawer>
  );
};

export default GlobalSearchMobileLeftPanel;
