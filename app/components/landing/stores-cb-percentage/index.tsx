'use client';
import React, {
  useRef,
  useState,
  // useEffect
} from 'react';
import CommonContainer from '../../common-container';
import Pattern2 from '../../svg/patterns/pattern2';
import shopImg from '@/public/img/shop.png';
import Image from 'next/image';
import StoreByCBCard from './store-by-cb-card';
import SlidingButton from '../../atoms/sliding-button';
import Pattern5 from '../../svg/patterns/pattern5';
import SectionSeeAllBtn from '../../atoms/section-see-all';
import { LeftRoundButton, RightRoundButton } from '../../atoms/rounded-buttons';
import { sideScroll } from '@/utils/helpers';
import { useRouter } from 'next/navigation';
import type { PromiseStatus } from '@/types/global-types';
import type { StoresByCbContextResponse } from '@/services/api/data-contracts';
import { useOverflowing } from '@/utils/custom-hooks';
import ThemeButton from '../../atoms/theme-btn';
import { ChevronRightCircle } from 'lucide-react';
// import Link from 'next/link';

const buttonDetails = [
  { title: '50% CB', value: '50pc' },
  { title: '25% CB ', value: '25pc' },
  { title: '100% CB', value: '100pc' },
];

// Generate placeholder array for skeleton loading
const generatePlaceholderArray = (count: number) => Array(count).fill(null);

const Index = ({
  storesByCBPercent,
  promiseStatus,
}: {
  storesByCBPercent: StoresByCbContextResponse | null;
  promiseStatus: PromiseStatus;
}) => {
  const [selectedPercent, setSelectedPercent] = useState<
    '50pc' | '25pc' | '100pc'
  >('50pc');
  const containerRef = useRef<HTMLDivElement | null>(null);
  const router = useRouter();

  const isOverflowing: boolean = useOverflowing(containerRef, selectedPercent);
  const isLoading = promiseStatus !== 'fulfilled';

  // useEffect(() => {
  //   storesByCBPercent?.[selectedPercent]?.forEach((item) => {
  //     router.prefetch(`/store/${formatStoreName(item.storeName)}`);
  //   });
  // }, [selectedPercent, router, storesByCBPercent]);

  // Only return null if both rejected and no data
  if (promiseStatus === 'rejected' && !storesByCBPercent) {
    return null;
  }

  return (
    <CommonContainer className='storesByPercentageSection lg:flex lg:rounded-none'>
      <div className='relative bg-transparent lg:bg-[#EAEAEA] lg:dark:bg-[#1f2022] lg:w-[162px] flex justify-center shrink-0'>
        <Pattern2 className='text-[#E2E2E2] dark:text-[#3B3D45] shrink-0 absolute top-[15px] left-[13px] lg:hidden' />
        <Pattern5 className='text-[#D7D7D7] dark:text-[#3B3D45] shrink-0 absolute hidden lg:block top-[15px] left-[13px]' />
        <div className='flex mt-[21px] lg:mt-0 items-center justify-center lg:flex-col lg:w-min text-center'>
          <Image
            alt='shop image'
            className='w-[40px] h-[40px] lg:w-[80px] lg:h-[80px]'
            quality={100}
            src={shopImg}
          />
          <h3 className='text-sm md:text-lg lg:text-sm lg:font-[400] text-heading font-medium font-pat ml-[11px] lg:ml-0'>
            Store by CB Percentage
          </h3>
          <SectionSeeAllBtn
            isMobile={false}
            onClick={() => router.push('online-free-shopping-stores')}
          />
        </div>
      </div>
      {isOverflowing && (
        <LeftRoundButton
          classCont='mt-[0px] ml-[12px]'
          onClick={() => sideScroll(containerRef.current, 10, 400, -10)}
        />
      )}

      <div className='overflow-hidden grow customScrollbar'>
        <div className='mt-[26px] lg:bg-white lg:dark:bg-[#191a1c] lg:mt-0 lg:py-[12px] lg:shadow'>
          <SlidingButton
            buttonDetails={buttonDetails}
            defaultSelectedBtn={1}
            onChange={(e) => setSelectedPercent(e.target.value)}
            uniqueId='storeByCB'
          />
        </div>

        <div
          className='grid grid-cols-2 min-[500px]:grid-cols-3 min-[700px]:grid-cols-4 gap-4 mt-[10px] px-[8px] lg:gap-6 lg:flex lg:pt-8 lg:mx-[30px] overflow-auto w-full lg:w-[calc(100%-60px)] customScrollbar lg:pb-[25px] min-h-[180px]'
          ref={containerRef}
        >
          {isLoading || !storesByCBPercent ? (
            // Render skeleton loaders when loading or no data
            generatePlaceholderArray(6).map((item) => (
              <StoreByCBCard
                bgColor='#f0f0f0'
                caption=''
                className='w-[130px] md:w-full lg:w-[180px]'
                isLoading={true}
                key={`skeleton-store-${item}`}
                saved={false}
                src=''
                storeName=''
                uid={item + 1000}
              />
            ))
          ) : (
            <>
              {storesByCBPercent[selectedPercent]?.map((item) => (
                <StoreByCBCard
                  bgColor={item.bgColor}
                  caption={item.caption}
                  className='w-[130px] md:w-full lg:w-[180px]'
                  key={item.uid}
                  saved={item.saved}
                  src={item.imageUrl}
                  storeName={item.storeName}
                  uid={item.uid}
                />
              )) || []}

              {/* See All Card */}
              <ThemeButton
                className='!w-fit text-base mx-auto uppercase mr-4 mt-[30px] whitespace-nowrap px-3'
                icon={<ChevronRightCircle className='size-6 ml-2' />}
                isDisabled={isLoading}
                onClick={() => router.push('online-free-shopping-stores')}
                text='See All Stores'
              />
            </>
          )}
        </div>
      </div>
      {isOverflowing && (
        <RightRoundButton
          classCont='mt-[0px] ml-[6px]'
          onClick={() => sideScroll(containerRef.current, 10, 400, 10)}
        />
      )}
      <SectionSeeAllBtn
        onClick={() => router.push('online-free-shopping-stores')}
      />
    </CommonContainer>
  );
};

export default Index;
