'use client';
import Image from 'next/image';
import React, { useEffect, useRef, useState } from 'react';
import CashbackSVG from '../../svg/cashback';
import clsx from 'clsx';
import cardMenuSVG from '@/public/svg/card-menu-btn.svg';
import OfferCardMenu from '../../dropdowns/offer-card-menu';
import { toast } from 'react-toastify';
import SaveSVG, { ShareSVG } from '../../svg/save';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { setShowLeftPanel } from '@/redux/slices/main-header-slice';
import { setLoginModalOpen } from '@/redux/slices/auth-slice';
import type {
  RemoveOfferDto,
  SaveOfferDto,
} from '@/services/api/data-contracts';
import fetchWrapper from '@/utils/fetch-wrapper';
import { useOnClickOutside } from 'usehooks-ts';
import ShimmerEffect, { RectShimmer } from '../../atoms/shimmer-effect';
import { formatStoreName } from '@/utils/helpers';
import SmartLink from '../../common/smart-link';
import { LinkType } from '@/utils/link-utils';
import { setShowWarningModal } from '@/redux/slices/global-slice';

const menuIcons = [
  <SaveSVG
    className='text-primary dark:text-[#A9AFC8] my-[14px] w-[10px] h-[12px]'
    fill={'transparent'}
    key={1}
  />,
  <ShareSVG
    className='text-primary dark:text-[#A9AFC8] my-[14px] w-[10px] h-[12px]'
    key={2}
  />,
];
const menuItems = [
  {
    key: 1,
    value: 'Save',
  },
  {
    key: 2,
    value: 'Share',
  },
];

const StoreByCBCard = ({
  alt,
  bgColor,
  caption,
  className,
  fromSavedScreen = false,
  isLoading = false,
  saved,
  src,
  storeName,
  uid,
  storePopUpWarning,
}: {
  alt?: string;
  bgColor: string;
  caption: string;
  className?: string;
  fromSavedScreen?: boolean;
  isLoading?: boolean;
  saved: boolean;
  src: string;
  storeName: string;
  uid: number;
  storePopUpWarning?: string;
}) => {
  const [isSaved, setIsSaved] = useState(false);
  const [showMenuDropdown, setShowMenuDropdown] = useState(false);
  const storeByCbCard = useRef<HTMLAnchorElement>(null);
  const dispatch = useAppDispatch();
  const { isUserLogin } = useAppSelector((state) => state.auth);

  const handleMenuItemClick = (_key: number, value: string) => {
    if (value === 'Save') {
      if (isUserLogin) {
        handleSaveStore();
      } else {
        dispatch(setShowLeftPanel(false));
        dispatch(setLoginModalOpen(true));
      }
    }

    // toast.info('Item has been saved successfully.');
  };
  const handleSaveStore = async () => {
    try {
      if (isSaved) {
        const body: RemoveOfferDto = {
          itemUid: uid,
        };
        await fetchWrapper('/api/proxy/stores/remove', {
          method: 'POST',
          body: JSON.stringify(body),
          suppressToast: true,
        });

        setIsSaved(false);
        toast.info('Store has been removed successfully.');
      } else {
        const body: SaveOfferDto = {
          itemUid: uid,
        };
        await fetchWrapper('/api/proxy/stores/save', {
          method: 'POST',
          body: JSON.stringify(body),
          suppressToast: true,
        });
        setIsSaved(true);
        toast.info('Store has been saved successfully.');
      }
    } catch (e: any) {
      const message =
        e?.message ?? 'Save store failed. Please try again later.';
      toast.error(message);
    }
  };

  useEffect(() => {
    setIsSaved(saved);
  }, [saved]);

  useOnClickOutside(storeByCbCard, () => setShowMenuDropdown(false));

  const handleCardClick = (e?: React.MouseEvent<Element, MouseEvent>) => {
    if (storePopUpWarning && e) {
      e.preventDefault();
      dispatch(setShowWarningModal(true));
    }
  };
  if (isLoading) {
    return (
      <div className={clsx(className, 'storeByCBCard lg:shrink-0')}>
        <div
          className='relative h-[100px] w-[162px] p-[10px] mx-auto flex items-center justify-center shadow-md rounded-[5px] transition-all duration-300'
          style={{ background: '#f0f0f0' }}
        >
          <ShimmerEffect className='w-full h-full' />
          {/* Add placeholder for menu button to maintain layout */}
          <div className='absolute top-[3px] right-[3px] w-[20px] h-[20px]'>
            <RectShimmer height='20px' width='20px' />
          </div>
        </div>

        <div className='flex justify-center mt-[7px]'>
          <RectShimmer height='12px' width='80px' />
        </div>
        <div className='text-content text-[12px] font-medium text-center block mt-[4px] mb-[8px]'>
          <RectShimmer className='mx-auto' height='12px' width='100px' />
        </div>
      </div>
    );
  }

  if (isSaved === false && fromSavedScreen) {
    return <></>;
  }

  return (
    <SmartLink
      className={clsx(
        className,
        'storeByCBCard lg:shrink-0 cursor-pointer bg-transparent border-0 text-inherit'
      )}
      href={`/store/${formatStoreName(storeName)}`}
      linkType={LinkType.INTERNAL}
      onClick={handleCardClick}
      ref={storeByCbCard}
    >
      <div
        className='relative aspect-[16/9] p-[10px] mx-auto bg-primary flex items-center justify-center shadow-md rounded-[5px] transition-all duration-300 hover:scale-105 hover:shadow-lg active:scale-[0.97]'
        style={{
          background: bgColor === '#70367c' ? '#ffffff' : bgColor,
        }}
      >
        <Image
          alt={alt || 'store image'}
          className='!static object-contain transition-opacity duration-200 hover:opacity-100'
          fill
          quality={100}
          src={src}
          style={{ opacity: 0.9 }}
        />
        <div
          className='absolute top-[3px] right-[3px]'
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            setShowMenuDropdown(!showMenuDropdown);
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.stopPropagation();
              e.preventDefault();
              setShowMenuDropdown(!showMenuDropdown);
            }
          }}
        >
          <Image
            alt='card menu'
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              setShowMenuDropdown(!showMenuDropdown);
            }}
            priority={true}
            quality={100}
            src={cardMenuSVG}
          />
          <OfferCardMenu
            cardType='store'
            icons={menuIcons}
            items={menuItems}
            onClickItem={(key: number, value: string) => {
              handleMenuItemClick(key, value);
              setTimeout(() => setShowMenuDropdown(false), 200);
            }}
            rootClass={clsx(
              showMenuDropdown ? 'block' : 'hidden',
              '!top-[22px]'
            )}
            saved={isSaved}
            shareUrl={`/store/${storeName}`}
          />
        </div>
      </div>

      <div className='flex justify-center mt-3'>
        <CashbackSVG className='text-primary dark:text-content' />
        <span className='text-xs text-primary dark:text-content font-bold ml-[4px]'>
          {caption}
        </span>
      </div>
      <span className='text-content text-[12px] font-medium text-center block mt-[4px] mb-[8px]'>
        {storeName}
      </span>
    </SmartLink>
  );
};

export default StoreByCBCard;
