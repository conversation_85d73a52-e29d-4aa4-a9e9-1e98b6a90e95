'use client';
import React from 'react';
import { Swiper } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';
import { EffectCoverflow, Pagination } from 'swiper/modules';
import clsx from 'clsx';
import { motion } from 'framer-motion';

const CoverflowSwiper = ({
  swiperName,
  children,
  className,
}: {
  swiperName: string;
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      className={clsx(className, 'coverflowSwiperWrapper')}
      initial={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.6, ease: 'easeOut' }}
    >
      <Swiper
        centeredSlides={true}
        className={swiperName}
        coverflowEffect={{
          rotate: 0,
          stretch: 0,
          depth: 100,
          modifier: 2,
          slideShadows: true,
        }}
        effect={'coverflow'}
        grabCursor={true}
        loop={true}
        modules={[EffectCoverflow, Pagination]}
        pagination={{ clickable: true, enabled: true }}
        slidesPerView={'auto'}
      >
        {children}
      </Swiper>
    </motion.div>
  );
};

export default CoverflowSwiper;
