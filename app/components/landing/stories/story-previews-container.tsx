'use client';

import React, { useEffect, useState } from 'react';
import StoryPreview from './story-preview';
import { useDispatch, useSelector } from 'react-redux';
import type { RootState } from '@/redux/store';
import Portal from '../../react-portal';
import StoriesContainer from './stories-container';
import { setStoriesSeen } from '@/redux/slices/stories-slice';
import type { PromiseStatus, ResponseStoriesData } from '@/types/global-types';

const StatusPreviewsContainer = ({
  storiesData,
  promiseStatus,
}: {
  storiesData: ResponseStoriesData[];
  promiseStatus: PromiseStatus;
}) => {
  // Move all hooks before any conditional logic or returns
  const { isStoryModalOpen, currentSlideIndex, storiesSeen } = useSelector(
    (state: RootState) => state.stories
  );
  const [sortedStories, setSortedStories] = useState(storiesData);
  const dispatch = useDispatch();

  useEffect(() => {
    const storedData = localStorage.getItem('storiesSeen');
    if (storedData) {
      dispatch(setStoriesSeen(JSON.parse(storedData)));
    }
  }, [dispatch]);

  useEffect(() => {
    // Define a custom sorting function to sort the stories based on seen stories
    if (!isStoryModalOpen) {
      const customSort = (a: any, b: any) => {
        const aSeenCount = storiesSeen[a.storeName] || 0;
        const bSeenCount = storiesSeen[b.storeName] || 0;

        // Compare based on the number of stories seen
        if (aSeenCount === a.stories.length) {
          return 1;
        }
        if (bSeenCount === b.stories.length) {
          return -1;
        }
        return 0;
      };
      const sortedData = storiesData.sort(customSort);
      setSortedStories([...sortedData]);
    }
  }, [storiesSeen, isStoryModalOpen, storiesData]);

  // Conditional returns after all hooks are called
  if (promiseStatus !== 'fulfilled') {
    return (
      <div className='lg:hidden mt-[38px] pb-[8px] px-[16px] flex gap-[16px] justify-start md:justify-center items-center overflow-auto'>
        {Array.from({ length: 6 }).map((item, i) => (
          <div
            className='w-[60px] h-[60px] rounded-[6px] bg-gray-200 dark:bg-gray-700 animate-pulse'
            key={`skeleton-${i}`}
          />
        ))}
      </div>
    );
  } else {
    return null;
  }

  return (
    <>
      <div className='lg:hidden mt-[38px] pb-[8px] px-[16px] flex gap-[16px] justify-start md:justify-center items-center overflow-auto'>
        {sortedStories.map((item, index) => (
          <StoryPreview
            img={item.storeLogo}
            key={item.storeName}
            slideIndex={index}
            storiesLen={item?.stories?.length || 0}
            storiesSeen={storiesSeen?.[item.storeName] ?? 0}
          />
        ))}
      </div>

      {isStoryModalOpen ? (
        <Portal>
          <StoriesContainer
            currentSlideIndex={currentSlideIndex}
            storiesData={storiesData}
          />
        </Portal>
      ) : null}
    </>
  );
};

export default StatusPreviewsContainer;
