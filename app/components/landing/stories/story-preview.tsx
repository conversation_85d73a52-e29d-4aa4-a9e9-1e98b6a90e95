'use client';
import Image from 'next/image';
import React from 'react';
import { useDispatch } from 'react-redux';
import {
  setCurrentSlideIndex,
  setIsStoryModalOpen,
} from '@/redux/slices/stories-slice';

const StatusPreview = ({
  img,
  slideIndex,
  storiesLen,
  storiesSeen,
}: {
  img: string;
  slideIndex: number;
  storiesLen: number;
  storiesSeen: number;
}) => {
  const unseenStoriesCount = storiesLen - storiesSeen;
  const numberOfDots = (2 * 3.14 * 27.5) / unseenStoriesCount;
  const color = unseenStoriesCount >= 1 ? '#8374FC' : '#CACAD0';

  const dispatch = useDispatch();

  const statusPreviewHandler = async () => {
    dispatch(setCurrentSlideIndex(slideIndex));
    dispatch(setIsStoryModalOpen(true));
  };

  return (
    <>
      <div className='relative' onClick={statusPreviewHandler}>
        <svg height='57' viewBox='0 0 57 57' width='57'>
          <circle
            cx='28.5'
            cy='28.5'
            fill='none'
            r='27.5'
            stroke={color}
            strokeDasharray={`${numberOfDots} 4 `}
            strokeDashoffset={numberOfDots}
            strokeWidth='2'
          />
        </svg>

        <div className='w-[49px] h-[49px] bg-[#8374FC] rounded-full absolute top-[4px] left-[4px]'>
          <div className='relative w-[49px] h-[49px] rounded-full mx-auto overflow-hidden'>
            <Image alt='img' className='object-contain' fill src={img || ''} />
          </div>
        </div>
      </div>
    </>
  );
};

export default StatusPreview;
