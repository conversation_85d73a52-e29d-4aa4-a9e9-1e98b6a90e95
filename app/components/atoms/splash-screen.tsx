'use client';
import React, { useEffect, useState } from 'react';
import PageLoader from '../misc/loading-components';

const SplashScreen = () => {
  const [showSplash, setShowSplash] = useState(true);

  useEffect(() => {
    setShowSplash(true);
    const rawData = localStorage.getItem('splashScreenShown');
    if (!rawData) {
      localStorage.setItem('splashScreenShown', JSON.stringify(true));
      const delay = async () => {
        // simulating a delay
        setShowSplash(true);
        await new Promise((resolve) => setTimeout(() => resolve(''), 3000));
        // After the delay, hide the splash screen
        setShowSplash(false);
      };
      delay();
    } else {
      setShowSplash(false);
    }
  }, []);

  return showSplash && <PageLoader isMainLoader={true} />;
};

export default SplashScreen;
