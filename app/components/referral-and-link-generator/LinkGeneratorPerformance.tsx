'use client';
import React, { useState, useEffect } from 'react';
import { linkGeneratorData } from './link-generator-data';
import LinkGeneratorTable from './LinkGeneratorTable';
import Image from 'next/image';
import clsx from 'clsx';
import { ArrowDown, Store } from 'lucide-react';
import SimpleDateRangePicker from '../dropdowns/simple-date-range-picker';
import ToolbarDropdown from '../atoms/toolbar-dropdown';
import ThemeButton from '../atoms/theme-btn';
import { useAppSelector } from '@/redux/hooks';
import fetchWrapper from '@/utils/fetch-wrapper';
import { toast } from 'react-toastify';

// Define interfaces for API responses
interface UserLinkResponse {
  linkId: string;
  shortUrl: string;
  storeName: string;
  totalClicks: number;
  convertedClicks: number;
  totalCashbackEarned: number;
  status?: string;
  createdAt: string;
}

interface MetricWithChange {
  value: number;
  percentageChange: number;
}

interface UserAnalyticsResponse {
  totalCashbackEarned: MetricWithChange;
  conversionRate: MetricWithChange;
  totalClicks: MetricWithChange;
}

const LinkGeneratorPerformance = () => {
  const { isUserLogin } = useAppSelector((state) => state.auth);
  const [userLinks, setUserLinks] = useState<UserLinkResponse[]>([]);
  const [analytics, setAnalytics] = useState([
    {
      title: 'Total Cashback earned',
      value: '₹0',
      type: 'increment',
      ratePercentage: 0,
      subTitle: 'from last period',
      image: '/temp/link-generator/rupee.png',
    },
    {
      title: 'Conversion Rate',
      value: '0%',
      type: 'increment',
      ratePercentage: 0,
      subTitle: 'from last period',
      image: '/temp/link-generator/conversion.png',
    },
    {
      title: 'Total Clicks',
      value: '0',
      type: 'increment',
      ratePercentage: 0,
      subTitle: 'from last period',
      image: '/temp/link-generator/click.png',
    },
  ]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    storeId: '',
    startDate: undefined as Date | undefined,
    endDate: undefined as Date | undefined,
    page: 1,
    limit: 10,
  });
  const [analyticsFilters, setAnalyticsFilters] = useState({
    storeId: '',
    startDate: undefined as Date | undefined,
    endDate: undefined as Date | undefined,
    periodType: 'month' as 'day' | 'week' | 'month' | 'year',
  });

  // Fetch user links on initial load
  useEffect(() => {
    if (isUserLogin) {
      fetchUserLinks();
      fetchUserAnalytics();
    }
  }, [isUserLogin]);

  // Function to fetch user links with current filters
  const fetchUserLinks = async () => {
    if (!isUserLogin) return;

    try {
      setLoading(true);
      const queryParams = new URLSearchParams();

      console.log('Current filters for API call:', {
        storeId: filters.storeId,
        startDate: filters.startDate
          ? filters.startDate.toISOString()
          : undefined,
        endDate: filters.endDate ? filters.endDate.toISOString() : undefined,
        page: filters.page,
        limit: filters.limit,
      });

      if (filters.storeId) queryParams.append('storeId', filters.storeId);

      // Make sure dates are properly formatted for the API
      if (filters.startDate) {
        const startDateStr = filters.startDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        console.log('Adding startDate to API call:', startDateStr);
        queryParams.append('startDate', startDateStr);
      }

      if (filters.endDate) {
        const endDateStr = filters.endDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        console.log('Adding endDate to API call:', endDateStr);
        queryParams.append('endDate', endDateStr);
      }

      queryParams.append('page', filters.page.toString());
      queryParams.append('limit', filters.limit.toString());

      console.log('Fetching links with params:', queryParams.toString());

      const response = await fetchWrapper<{
        items: UserLinkResponse[];
        total: number;
        page: number;
        pages: number;
        limit: number;
      }>(`/api/proxy/links?${queryParams.toString()}`);

      if (response?.items) {
        setUserLinks(response.items);
        console.log('Links fetched:', response.items.length);
      }
    } catch (error) {
      console.error('Error fetching user links:', error);
      toast.error('Failed to load your links. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch user links with a specific store ID
  const fetchUserLinksWithStoreId = async (storeId: string) => {
    if (!isUserLogin) return;

    try {
      setLoading(true);
      const queryParams = new URLSearchParams();

      console.log('Fetching with store ID and current filters:', {
        storeId: storeId,
        startDate: filters.startDate
          ? filters.startDate.toISOString()
          : undefined,
        endDate: filters.endDate ? filters.endDate.toISOString() : undefined,
        page: filters.page,
        limit: filters.limit,
      });

      if (storeId) queryParams.append('storeId', storeId);

      // Make sure dates are properly formatted for the API
      if (filters.startDate) {
        const startDateStr = filters.startDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        console.log('Adding startDate to store ID API call:', startDateStr);
        queryParams.append('startDate', startDateStr);
      }

      if (filters.endDate) {
        const endDateStr = filters.endDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        console.log('Adding endDate to store ID API call:', endDateStr);
        queryParams.append('endDate', endDateStr);
      }

      queryParams.append('page', filters.page.toString());
      queryParams.append('limit', filters.limit.toString());

      console.log(
        'Fetching links with store ID:',
        storeId,
        queryParams.toString()
      );

      const response = await fetchWrapper<{
        items: UserLinkResponse[];
        total: number;
        page: number;
        pages: number;
        limit: number;
      }>(`/api/proxy/links?${queryParams.toString()}`);

      if (response?.items) {
        setUserLinks(response.items);
        console.log('Links fetched with store ID:', response.items.length);
      }
    } catch (error) {
      console.error('Error fetching user links:', error);
      toast.error('Failed to load your links. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchUserAnalytics = async () => {
    if (!isUserLogin) return;

    try {
      setLoading(true);
      const queryParams = new URLSearchParams();

      console.log('Analytics filters for API call:', {
        storeId: analyticsFilters.storeId,
        startDate: analyticsFilters.startDate
          ? analyticsFilters.startDate.toISOString()
          : undefined,
        endDate: analyticsFilters.endDate
          ? analyticsFilters.endDate.toISOString()
          : undefined,
        periodType: analyticsFilters.periodType,
      });

      if (analyticsFilters.storeId)
        queryParams.append('storeId', analyticsFilters.storeId);

      // Make sure dates are properly formatted for the API
      if (analyticsFilters.startDate) {
        const startDateStr = analyticsFilters.startDate
          .toISOString()
          .split('T')[0]; // Format as YYYY-MM-DD
        console.log('Adding startDate to analytics API call:', startDateStr);
        queryParams.append('startDate', startDateStr);
      }

      if (analyticsFilters.endDate) {
        const endDateStr = analyticsFilters.endDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        console.log('Adding endDate to analytics API call:', endDateStr);
        queryParams.append('endDate', endDateStr);
      }

      queryParams.append('periodType', analyticsFilters.periodType);

      console.log('Fetching analytics with params:', queryParams.toString());

      const response = await fetchWrapper<UserAnalyticsResponse>(
        `/api/proxy/links/analytics?${queryParams.toString()}`
      );

      if (response) {
        setAnalytics([
          {
            title: 'Total Cashback earned',
            value: `₹${response.totalCashbackEarned?.value || 0}`,
            type:
              (response.totalCashbackEarned?.percentageChange || 0) >= 0
                ? 'increment'
                : 'decrement',
            ratePercentage: Math.abs(
              response.totalCashbackEarned?.percentageChange || 0
            ),
            subTitle: 'from last period',
            image: '/temp/link-generator/rupee.png',
          },
          {
            title: 'Conversion Rate',
            value: `${response.conversionRate?.value || 0}%`,
            type:
              (response.conversionRate?.percentageChange || 0) >= 0
                ? 'increment'
                : 'decrement',
            ratePercentage: Math.abs(
              response.conversionRate?.percentageChange || 0
            ),
            subTitle: 'from last period',
            image: '/temp/link-generator/conversion.png',
          },
          {
            title: 'Total Clicks',
            value: `${response.totalClicks?.value || 0}`,
            type:
              (response.totalClicks?.percentageChange || 0) >= 0
                ? 'increment'
                : 'decrement',
            ratePercentage: Math.abs(
              response.totalClicks?.percentageChange || 0
            ),
            subTitle: 'from last period',
            image: '/temp/link-generator/click.png',
          },
        ]);
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
      // Don't show toast for analytics failure to avoid too many error messages
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch analytics with a specific store ID
  const fetchUserAnalyticsWithStoreId = async (storeId: string) => {
    if (!isUserLogin) return;

    try {
      setLoading(true);
      const queryParams = new URLSearchParams();

      console.log('Analytics with store ID and current filters:', {
        storeId: storeId,
        startDate: analyticsFilters.startDate
          ? analyticsFilters.startDate.toISOString()
          : undefined,
        endDate: analyticsFilters.endDate
          ? analyticsFilters.endDate.toISOString()
          : undefined,
        periodType: analyticsFilters.periodType,
      });

      if (storeId) queryParams.append('storeId', storeId);

      // Make sure dates are properly formatted for the API
      if (analyticsFilters.startDate) {
        const startDateStr = analyticsFilters.startDate
          .toISOString()
          .split('T')[0]; // Format as YYYY-MM-DD
        console.log(
          'Adding startDate to analytics store ID API call:',
          startDateStr
        );
        queryParams.append('startDate', startDateStr);
      }

      if (analyticsFilters.endDate) {
        const endDateStr = analyticsFilters.endDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        console.log(
          'Adding endDate to analytics store ID API call:',
          endDateStr
        );
        queryParams.append('endDate', endDateStr);
      }

      queryParams.append('periodType', analyticsFilters.periodType);

      console.log(
        'Fetching analytics with store ID:',
        storeId,
        queryParams.toString()
      );

      const response = await fetchWrapper<UserAnalyticsResponse>(
        `/api/proxy/links/analytics?${queryParams.toString()}`
      );

      if (response) {
        setAnalytics([
          {
            title: 'Total Cashback earned',
            value: `₹${response.totalCashbackEarned?.value || 0}`,
            type:
              (response.totalCashbackEarned?.percentageChange || 0) >= 0
                ? 'increment'
                : 'decrement',
            ratePercentage: Math.abs(
              response.totalCashbackEarned?.percentageChange || 0
            ),
            subTitle: 'from last period',
            image: '/temp/link-generator/rupee.png',
          },
          {
            title: 'Conversion Rate',
            value: `${response.conversionRate?.value || 0}%`,
            type:
              (response.conversionRate?.percentageChange || 0) >= 0
                ? 'increment'
                : 'decrement',
            ratePercentage: Math.abs(
              response.conversionRate?.percentageChange || 0
            ),
            subTitle: 'from last period',
            image: '/temp/link-generator/conversion.png',
          },
          {
            title: 'Total Clicks',
            value: `${response.totalClicks?.value || 0}`,
            type:
              (response.totalClicks?.percentageChange || 0) >= 0
                ? 'increment'
                : 'decrement',
            ratePercentage: Math.abs(
              response.totalClicks?.percentageChange || 0
            ),
            subTitle: 'from last period',
            image: '/temp/link-generator/click.png',
          },
        ]);
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
      // Don't show toast for analytics failure to avoid too many error messages
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateNewLink = () => {
    if (!isUserLogin) {
      // Show login popup if not logged in
      toast.info('Please log in to generate a new link');
      return;
    }

    // Navigate to the top of the page where the link generator is
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <section className='w-full py-12 md:py-24 flex flex-col items-center gap-y-4 bg-[#EEEEEE] dark:bg-[#212327] rounded-lg shadow-lg px-4 md:px-8'>
      <h2 className='text-lg font-medium text-primary text-left w-full max-w-6xl'>
        Your Shared Links Performance
      </h2>

      <div className='flex items-center justify-between gap-x-[8px] w-full max-w-6xl my-3'>
        <div className='flex items-center justify-start gap-x-[8px]'>
          <ToolbarDropdown
            className={clsx('!flex py-5 px-3 !w-[120px]')}
            items={[
              {
                label: 'All Stores',
                key: 'all',
              },
              {
                label: 'Amazon',
                key: '66f206ec28628113b4856900',
              },
              {
                label: 'Flipkart',
                key: '66f206ec28628113b48568ea',
              },
            ]}
            name='All Stores'
            onClick={(item) => {
              const storeId = typeof item === 'string' ? item : item.key;
              const storeIdValue = storeId === 'all' ? '' : storeId.toString();

              // Update links filters
              const newFilters = {
                ...filters,
                storeId: storeIdValue,
              };
              setFilters(newFilters);

              // Update analytics filters with the same store ID
              const newAnalyticsFilters = {
                ...analyticsFilters,
                storeId: storeIdValue,
              };
              setAnalyticsFilters(newAnalyticsFilters);

              // Fetch updated data with the new storeId directly
              fetchUserLinksWithStoreId(storeIdValue);
              fetchUserAnalyticsWithStoreId(storeIdValue);
            }}
          >
            <Store className='w-[14px] lg:w-[20px] text-[#7366D9] dark:text-white transition-transform duration-300 hover:scale-110' />
          </ToolbarDropdown>

          {/* Date picker */}
          <SimpleDateRangePicker
            rootClassName={clsx('flex !w-[120px]')}
            onChange={(dates) => {
              console.log('Date picker onChange called with dates:', dates);

              if (dates === null) {
                // Handle clearing dates
                console.log('Received clear dates signal from date picker');

                // Create new filter objects without dates
                const newFilters = {
                  ...filters,
                  startDate: undefined,
                  endDate: undefined,
                };

                const newAnalyticsFilters = {
                  ...analyticsFilters,
                  startDate: undefined,
                  endDate: undefined,
                };

                // Update state
                setFilters(newFilters);
                setAnalyticsFilters(newAnalyticsFilters);

                // Directly fetch data without waiting for state updates
                console.log('Directly fetching data without date filters');

                // Use a dedicated function to fetch with cleared filters
                const fetchWithClearedFilters = async () => {
                  try {
                    setLoading(true);

                    // Prepare query params for links (without dates)
                    const linksParams = new URLSearchParams();
                    if (newFilters.storeId)
                      linksParams.append('storeId', newFilters.storeId);
                    linksParams.append('page', newFilters.page.toString());
                    linksParams.append('limit', newFilters.limit.toString());

                    console.log(
                      'Fetching links with cleared date filters:',
                      linksParams.toString()
                    );

                    // Fetch links
                    const linksResponse = await fetchWrapper<{
                      items: UserLinkResponse[];
                      total: number;
                      page: number;
                      pages: number;
                      limit: number;
                    }>(`/api/proxy/links?${linksParams.toString()}`);

                    if (linksResponse?.items) {
                      setUserLinks(linksResponse.items);
                      console.log(
                        'Links fetched with cleared dates:',
                        linksResponse.items.length
                      );
                    }

                    // Prepare query params for analytics (without dates)
                    const analyticsParams = new URLSearchParams();
                    if (newAnalyticsFilters.storeId)
                      analyticsParams.append(
                        'storeId',
                        newAnalyticsFilters.storeId
                      );
                    analyticsParams.append(
                      'periodType',
                      newAnalyticsFilters.periodType
                    );

                    console.log(
                      'Fetching analytics with cleared date filters:',
                      analyticsParams.toString()
                    );

                    // Fetch analytics
                    const analyticsResponse =
                      await fetchWrapper<UserAnalyticsResponse>(
                        `/api/proxy/links/analytics?${analyticsParams.toString()}`
                      );

                    if (analyticsResponse) {
                      setAnalytics([
                        {
                          title: 'Total Cashback earned',
                          value: `₹${
                            analyticsResponse.totalCashbackEarned?.value || 0
                          }`,
                          type:
                            (analyticsResponse.totalCashbackEarned
                              ?.percentageChange || 0) >= 0
                              ? 'increment'
                              : 'decrement',
                          ratePercentage: Math.abs(
                            analyticsResponse.totalCashbackEarned
                              ?.percentageChange || 0
                          ),
                          subTitle: 'from last period',
                          image: '/temp/link-generator/rupee.png',
                        },
                        {
                          title: 'Conversion Rate',
                          value: `${
                            analyticsResponse.conversionRate?.value || 0
                          }%`,
                          type:
                            (analyticsResponse.conversionRate
                              ?.percentageChange || 0) >= 0
                              ? 'increment'
                              : 'decrement',
                          ratePercentage: Math.abs(
                            analyticsResponse.conversionRate
                              ?.percentageChange || 0
                          ),
                          subTitle: 'from last period',
                          image: '/temp/link-generator/conversion.png',
                        },
                        {
                          title: 'Total Clicks',
                          value: `${analyticsResponse.totalClicks?.value || 0}`,
                          type:
                            (analyticsResponse.totalClicks?.percentageChange ||
                              0) >= 0
                              ? 'increment'
                              : 'decrement',
                          ratePercentage: Math.abs(
                            analyticsResponse.totalClicks?.percentageChange || 0
                          ),
                          subTitle: 'from last period',
                          image: '/temp/link-generator/click.png',
                        },
                      ]);
                    }
                  } catch (error) {
                    console.error(
                      'Error fetching data with cleared filters:',
                      error
                    );
                    toast.error(
                      'Failed to load data after clearing date filters.'
                    );
                  } finally {
                    setLoading(false);
                  }
                };

                // Execute the fetch function
                fetchWithClearedFilters();

                return;
              }

              if (dates?.startDate && dates?.endDate) {
                console.log('Setting new date filters:', {
                  startDate: dates.startDate.toISOString(),
                  endDate: dates.endDate.toISOString(),
                });

                // Create new filter objects
                const newFilters = {
                  ...filters,
                  startDate: dates.startDate,
                  endDate: dates.endDate,
                };

                const newAnalyticsFilters = {
                  ...analyticsFilters,
                  startDate: dates.startDate,
                  endDate: dates.endDate,
                };

                // Update state
                setFilters(newFilters);
                setAnalyticsFilters(newAnalyticsFilters);

                // Immediately fetch data with the new filters
                console.log('Immediately fetching data with new date filters');

                // Use the new filter objects directly in the API calls
                const fetchWithNewFilters = async () => {
                  try {
                    setLoading(true);

                    // Prepare query params for links
                    const linksParams = new URLSearchParams();
                    if (newFilters.storeId)
                      linksParams.append('storeId', newFilters.storeId);
                    if (newFilters.startDate) {
                      const startDateStr = newFilters.startDate
                        .toISOString()
                        .split('T')[0];
                      linksParams.append('startDate', startDateStr);
                    }
                    if (newFilters.endDate) {
                      const endDateStr = newFilters.endDate
                        .toISOString()
                        .split('T')[0];
                      linksParams.append('endDate', endDateStr);
                    }
                    linksParams.append('page', newFilters.page.toString());
                    linksParams.append('limit', newFilters.limit.toString());

                    console.log(
                      'Fetching links with params:',
                      linksParams.toString()
                    );

                    // Fetch links
                    const linksResponse = await fetchWrapper<{
                      items: UserLinkResponse[];
                      total: number;
                      page: number;
                      pages: number;
                      limit: number;
                    }>(`/api/proxy/links?${linksParams.toString()}`);

                    if (linksResponse?.items) {
                      setUserLinks(linksResponse.items);
                      console.log('Links fetched:', linksResponse.items.length);
                    }

                    // Prepare query params for analytics
                    const analyticsParams = new URLSearchParams();
                    if (newAnalyticsFilters.storeId)
                      analyticsParams.append(
                        'storeId',
                        newAnalyticsFilters.storeId
                      );
                    if (newAnalyticsFilters.startDate) {
                      const startDateStr = newAnalyticsFilters.startDate
                        .toISOString()
                        .split('T')[0];
                      analyticsParams.append('startDate', startDateStr);
                    }
                    if (newAnalyticsFilters.endDate) {
                      const endDateStr = newAnalyticsFilters.endDate
                        .toISOString()
                        .split('T')[0];
                      analyticsParams.append('endDate', endDateStr);
                    }
                    analyticsParams.append(
                      'periodType',
                      newAnalyticsFilters.periodType
                    );

                    console.log(
                      'Fetching analytics with params:',
                      analyticsParams.toString()
                    );

                    // Fetch analytics
                    const analyticsResponse =
                      await fetchWrapper<UserAnalyticsResponse>(
                        `/api/proxy/links/analytics?${analyticsParams.toString()}`
                      );

                    if (analyticsResponse) {
                      setAnalytics([
                        {
                          title: 'Total Cashback earned',
                          value: `₹${
                            analyticsResponse.totalCashbackEarned?.value || 0
                          }`,
                          type:
                            (analyticsResponse.totalCashbackEarned
                              ?.percentageChange || 0) >= 0
                              ? 'increment'
                              : 'decrement',
                          ratePercentage: Math.abs(
                            analyticsResponse.totalCashbackEarned
                              ?.percentageChange || 0
                          ),
                          subTitle: 'from last period',
                          image: '/temp/link-generator/rupee.png',
                        },
                        {
                          title: 'Conversion Rate',
                          value: `${
                            analyticsResponse.conversionRate?.value || 0
                          }%`,
                          type:
                            (analyticsResponse.conversionRate
                              ?.percentageChange || 0) >= 0
                              ? 'increment'
                              : 'decrement',
                          ratePercentage: Math.abs(
                            analyticsResponse.conversionRate
                              ?.percentageChange || 0
                          ),
                          subTitle: 'from last period',
                          image: '/temp/link-generator/conversion.png',
                        },
                        {
                          title: 'Total Clicks',
                          value: `${analyticsResponse.totalClicks?.value || 0}`,
                          type:
                            (analyticsResponse.totalClicks?.percentageChange ||
                              0) >= 0
                              ? 'increment'
                              : 'decrement',
                          ratePercentage: Math.abs(
                            analyticsResponse.totalClicks?.percentageChange || 0
                          ),
                          subTitle: 'from last period',
                          image: '/temp/link-generator/click.png',
                        },
                      ]);
                    }
                  } catch (error) {
                    console.error(
                      'Error fetching data with new filters:',
                      error
                    );
                    toast.error(
                      'Failed to load data with the selected date range.'
                    );
                  } finally {
                    setLoading(false);
                  }
                };

                // Execute the fetch function
                fetchWithNewFilters();
              } else {
                console.log(
                  'Date picker onChange called but dates are incomplete:',
                  dates
                );
              }
            }}
          />
        </div>
        <ThemeButton
          className='!w-[150px] !h-[40px]'
          onClick={handleGenerateNewLink}
          text='Generate New Link'
        />
      </div>
      {isUserLogin ? (
        loading ? (
          <div className='w-full max-w-6xl bg-white dark:bg-[#212327] p-6 rounded-lg text-center'>
            <p className='text-lg'>Loading...</p>
          </div>
        ) : userLinks.length > 0 ? (
          <LinkGeneratorTable
            links={userLinks.map((link) => ({
              productName: link.shortUrl || 'Unknown',
              storeName: link.storeName || 'Unknown',
              clicks: link.totalClicks || 0,
              conversations: link.convertedClicks || 0,
              cashbackEarned: link.totalCashbackEarned || 0,
              status: 'approved' as 'approved' | 'pending' | 'rejected', // Default to approved since status is missing
            }))}
          />
        ) : (
          <div className='w-full max-w-6xl bg-white dark:bg-[#212327] p-6 rounded-lg text-center'>
            <p className='text-lg'>No data to show</p>
            <p className='text-sm text-gray-500 mt-2'>
              Try changing your filters or generate new links
            </p>
          </div>
        )
      ) : (
        <div className='w-full max-w-6xl bg-white dark:bg-[#212327] p-6 rounded-lg text-center'>
          <p className='text-lg'>
            Please log in to view your shared links performance
          </p>
        </div>
      )}

      <h2 className='text-lg font-medium text-primary text-left w-full max-w-6xl mt-12 mb-6'>
        Your Performance Analytics
      </h2>
      <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 w-full max-w-6xl'>
        {analytics.map((item) => (
          <div
            className='bg-white dark:bg-[#212327] rounded-lg p-4 min-w-[300px] w-full'
            key={item.title}
          >
            <div className='flex items-start gap-x-2 w-full justify-between'>
              <div className='flex flex-col items-center gap-y-1.5 mr-2 w-'>
                <h3 className='text-lg font-semibold text-left w-full max-w-6xl'>
                  {item.value}
                </h3>
                <p className='text-sm font-light text-left w-full max-w-6xl'>
                  {item.title}
                </p>
              </div>
              <Image
                alt={item.title}
                className='mt-1 w-9 h-9'
                height={100}
                src={item.image}
                width={100}
              />
            </div>
            <p
              className={clsx(
                'text-xs font-light text-left w-full max-w-6xl flex items-center gap-x-1 mt-2',
                item.type === 'increment' ? 'text-green-500' : 'text-red-500'
              )}
            >
              <ArrowDown
                className={clsx(
                  'w-4 h-4',
                  item.type === 'increment' ? 'rotate-180' : ''
                )}
              />
              {item.ratePercentage}% {item.subTitle}
            </p>
          </div>
        ))}
      </div>
    </section>
  );
};

export default LinkGeneratorPerformance;
