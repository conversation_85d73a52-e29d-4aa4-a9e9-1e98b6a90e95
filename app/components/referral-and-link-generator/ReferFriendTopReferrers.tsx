'use client';
import React, { useEffect } from 'react';
import { useState } from 'react';
import { motion } from 'framer-motion';
import ReferrersTable from './ReferrersTable';
import { GetReferralLeaderboardResponse } from '@/services/api/data-contracts';
import { usePathname, useSearchParams } from 'next/navigation';
import { useCreateQueryString } from '@/utils/custom-hooks';
import { useRouter } from 'next/navigation';

const tabs = [
  { label: 'All Time', value: 'all' },
  { label: 'This Month', value: 'monthly' },
  { label: 'This Week', value: 'weekly' },
];
const ReferFriendTopReferrers = ({
  data,
}: {
  data: GetReferralLeaderboardResponse[];
}) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const createQueryString = useCreateQueryString(searchParams);
  const [selectedTab, setSelectedTab] = useState('all');
  const { replace } = useRouter();

  const onChangeTab = (value: (typeof tabs)[number]['value']) => {
    setSelectedTab(value);
    replace(pathname + '?' + createQueryString('timeFrame', value));
  };

  useEffect(() => {
    const timeFrame = searchParams.get('timeFrame');
    if (timeFrame) {
      setSelectedTab(timeFrame);
    }
  }, [searchParams]);

  // Determine the heading text based on selected tab
  const getHeadingText = () => {
    switch (selectedTab) {
      case 'all':
        return 'Top Referrers All Time';
      case 'monthly':
        return 'Top Referrers This Month';
      case 'weekly':
        return 'Top Referrers This Week';
      default:
        return 'Top Referrers';
    }
  };

  return (
    <section className='w-full py-12 md:py-24 flex flex-col items-center gap-y-4 bg-[#EEEEEE] dark:bg-[#212327] rounded-lg shadow-lg px-4 md:px-8'>
      {/* How it works content goes here */}
      <div className='px-4 py-2 rounded-full text-sm text-primary bg-[#E6E2FF]'>
        Earn More
      </div>
      <h2 className='text-3xl md:text-4xl font-bold text-primary text-center'>
        {getHeadingText()}
      </h2>
      <p className='text-center max-w-xl text-sm font-light px-4'>
        See who's leading the way and earning the most rewards
      </p>
      <div className='flex justify-center mb-6'>
        <div className='relative flex bg-gray-200 rounded-full p-1'>
          {tabs.map((tab) => (
            <button
              className='relative px-6 py-2 rounded-full text-sm font-medium transition-all'
              key={tab.value}
              onClick={() => onChangeTab(tab.value)}
              style={{ minWidth: 100, zIndex: 1 }}
              type='button'
            >
              {selectedTab === tab.value && (
                <motion.div
                  className='absolute inset-0 rounded-full bg-primary shadow-lg'
                  layoutId='tab-pill'
                  style={{ zIndex: -1 }}
                  transition={{
                    type: 'spring',
                    stiffness: 500,
                    damping: 30,
                  }}
                />
              )}
              <span
                className={
                  selectedTab === tab.value ? 'text-white' : 'text-black'
                }
              >
                {tab.label}
              </span>
            </button>
          ))}
        </div>
      </div>

      <ReferrersTable referrers={data} />
    </section>
  );
};

export default ReferFriendTopReferrers;
