'use client';
import Image from 'next/image';
import React from 'react';
import { useState } from 'react';
import { Copy, Share } from 'lucide-react';
import Telegram from '../svg/social/telegram';
import { toast } from 'react-toastify';
import WhatsappSVG from '../svg/social/whatsapp';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { setLoginModalOpen } from '@/redux/slices/auth-slice';
import fetchWrapper from '@/utils/fetch-wrapper';
import { BASE_URL } from '@/config';

const LinkGeneratorHero = () => {
  return (
    <section className='w-full h-[730px] md:h-[630px] mb-[10vh] bg-gradient-to-r from-[#7366D9] to-[#574ABE] flex flex-col md:flex-row items-center justify-center px-4 md:px-8 py-6 rounded-lg shadow-lg'>
      {/* Hero content goes here */}
      <div className='text-center py-8 h-full w-full flex flex-col items-center justify-start gap-y-3 order-2 md:order-1'>
        <Image
          alt='referral hero'
          className='w-auto h-24 md:h-32 object-cover'
          height={300}
          src={'/temp/link-generator/hero.png'}
          width={300}
        />
        <h1 className='text-white text-2xl md:text-3xl lg:text-5xl font-medium mb-2 md:mb-4'>
          Share & Earn with IndianCashback
        </h1>
        <p className='text-white text-sm md:text-base lg:text-xl mb-4 md:mb-6'>
          Paste a product link, generate your unique cashback link, and start
          earning when others shop through it.
        </p>
        <LinkGeneratorHeroCard />
      </div>
    </section>
  );
};

export default LinkGeneratorHero;

const LinkGeneratorHeroCard = () => {
  const [productLink, setProductLink] = useState('');
  const [generatedLink, setGeneratedLink] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const { isUserLogin } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();

  const handleGenerateLink = async () => {
    if (!productLink.trim()) {
      toast.error('Please enter a valid product link');
      return;
    }

    // Check if user is logged in
    if (!isUserLogin) {
      // Show login popup if not logged in
      dispatch(setLoginModalOpen(true));
      return;
    }

    // User is logged in, proceed with link generation
    try {
      setIsGenerating(true);
      const response = await fetchWrapper<{ shortUrl: string }>(
        '/api/proxy/links/generate',
        {
          method: 'POST',
          body: JSON.stringify({ url: productLink }),
        }
      );

      if (response?.shortUrl) {
        setGeneratedLink(response.shortUrl);
        toast.success('Link generated successfully!');
      } else {
        toast.error('Failed to generate link. Please try again.');
      }
    } catch (error) {
      console.error('Error generating link:', error);
      toast.error('An error occurred while generating the link');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleShare = () => {
    if (!generatedLink) {
      toast.error('Please generate a link first');
      return;
    }

    if (navigator.share) {
      navigator.share({
        title: 'My ICB Cashback Link',
        text: 'Shop with my link and save!',
        url: generatedLink,
      });
    }
  };

  const handleTelegramShare = () => {
    if (!generatedLink) {
      toast.error('Please generate a link first');
      return;
    }

    // Telegram share implementation
    window.open(
      `https://t.me/share/url?url=${encodeURIComponent(
        generatedLink
      )}&text=${encodeURIComponent('Shop with my ICB cashback link!')}`,
      '_blank'
    );
  };

  const handleWhatsAppShare = () => {
    if (!generatedLink) {
      toast.error('Please generate a link first');
      return;
    }

    // WhatsApp share implementation
    window.open(
      `https://wa.me/?text=${encodeURIComponent(
        `Shop with my ICB cashback link: ${generatedLink}`
      )}`,
      '_blank'
    );
  };

  const copyToClipboard = () => {
    if (!generatedLink) {
      toast.error('Please generate a link first');
      return;
    }

    navigator.clipboard.writeText(generatedLink);
    toast.success('Link copied to clipboard');
  };

  return (
    <div className='w-full bg-[#EEEEEE] dark:bg-[#212327] p-8 rounded-lg h-[550px] md:h-[410px] shadow-md'>
      <div className='max-w-3xl mx-auto'>
        <h1 className='text-2xl md:text-3xl font-bold mb-4 md:mb-6'>
          Generate your cashback link
        </h1>

        <p className='mb-2 text-sm md:text-base'>
          Paste Any Valid Product Link From Supported Stores
        </p>

        <div className='flex mb-6 md:mb-8'>
          <input
            className='flex-grow p-3 rounded-l-lg focus:outline-none'
            onChange={(e) => setProductLink(e.target.value)}
            placeholder='eg. https://Www.Amazon.In/Dp/B09ZL5...'
            type='text'
            value={productLink}
          />
          <button
            className='bg-indigo-500 text-white px-6 py-3 rounded-r-lg hover:bg-indigo-600 disabled:bg-indigo-300 disabled:cursor-not-allowed'
            onClick={handleGenerateLink}
            type='button'
            disabled={isGenerating}
          >
            {isGenerating ? 'Generating...' : 'Generate'}
          </button>
        </div>

        <p className='mb-2 text-sm md:text-base'>Your ICB Cashback Link</p>
        <div className='flex items-center rounded-lg mb-6 md:mb-8'>
          <input
            className='flex-grow focus:outline-none p-3 rounded-l-lg bg-white dark:bg-[#3b3b3b]'
            readOnly
            type='text'
            value={generatedLink}
            placeholder='Your generated link will appear here'
          />
          <button
            className='text-indigo-500 bg-white dark:bg-[#3b3b3b] h-full py-3 pr-2 rounded-r-lg'
            onClick={copyToClipboard}
            type='button'
            disabled={!generatedLink}
          >
            <Copy className={`h-6 w-6 ${!generatedLink ? 'opacity-50' : ''}`} />
          </button>
        </div>

        <div className='flex flex-col md:flex-row justify-center gap-2 text-sm font-medium w-full'>
          <button
            className='w-full md:w-[155px] flex items-center justify-center gap-2 bg-indigo-500 text-white px-6 py-3 rounded-lg hover:bg-indigo-600 hover:scale-105 transition-all duration-300 disabled:bg-indigo-300 disabled:hover:scale-100 disabled:cursor-not-allowed'
            onClick={handleShare}
            type='button'
            disabled={!generatedLink || isGenerating}
          >
            <Share className='h-5 w-5' />
            Share
          </button>

          <button
            className='w-full md:w-[155px] flex items-center justify-center gap-2 bg-white text-primary px-6 py-3 rounded-lg border hover:bg-gray-50 hover:scale-105 transition-all duration-300 disabled:bg-gray-100 disabled:text-gray-400 disabled:hover:scale-100 disabled:cursor-not-allowed'
            onClick={handleTelegramShare}
            type='button'
            disabled={!generatedLink || isGenerating}
          >
            <Telegram
              className={`h-6 w-6 ${
                !generatedLink || isGenerating
                  ? 'text-gray-400'
                  : 'text-blue-500'
              }`}
            />
            Telegram
          </button>

          <button
            className='w-full md:w-[155px] flex items-center justify-center gap-2 bg-white text-primary px-6 py-3 rounded-lg border hover:bg-gray-50 hover:scale-105 transition-all duration-300 disabled:bg-gray-100 disabled:text-gray-400 disabled:hover:scale-100 disabled:cursor-not-allowed'
            onClick={handleWhatsAppShare}
            type='button'
            disabled={!generatedLink || isGenerating}
          >
            <WhatsappSVG
              className={`h-6 w-6 ${
                !generatedLink || isGenerating
                  ? 'text-gray-400'
                  : 'text-green-500'
              }`}
            />
            WhatsApp
          </button>
        </div>
      </div>
    </div>
  );
};
