'use client';
import { ArrowRightIcon } from 'lucide-react';
import Image from 'next/image';
import React from 'react';
import { useDispatch } from 'react-redux';
import { setLoginModalOpen } from '@/redux/slices/auth-slice';
import { useAppSelector } from '@/redux/hooks';
import { useRouter } from 'next/navigation';
const ReferFriendHeroSection = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { isUserLogin } = useAppSelector((state) => state.auth);

  const handleStartReferring = () => {
    // assign /referrals to local storage
    if (isUserLogin) {
      router.push('/refer-friends');
    } else {
      localStorage.setItem('redirectTo', '/refer-friends');
      dispatch(setLoginModalOpen(true));
    }
  };

  return (
    <section className='w-full min-h-[350px] bg-gradient-to-r from-[#7366D9] to-[#574ABE] flex flex-col md:flex-row items-center justify-center px-4 md:px-8 py-6 rounded-lg shadow-lg'>
      {/* Hero content goes here */}
      <div className='text-white text-center py-8 md:py-16 w-full md:w-1/2 h-full flex flex-col items-center justify-end gap-y-3 order-2 md:order-1'>
        <Image
          alt='referral hero'
          className='w-24 md:w-32 h-24 md:h-32 object-cover'
          height={300}
          src={'/temp/referrals/coin.png'}
          width={300}
        />
        <h1 className='text-2xl md:text-3xl lg:text-5xl font-medium mb-2 md:mb-4'>
          Refer & Earn with IndianCashback
        </h1>
        <p className='text-sm md:text-base lg:text-xl mb-4 max-w-[568px]'>
          Invite Friends. Earn Rewards. Win Top Spots On The Leaderboard!
        </p>
        <button
          className='bg-[#FFC554] text-black font-medium py-2 rounded shadow hover:bg-[#FFC554]/80 hover:scale-105 transition text-base px-6 flex items-center justify-center gap-x-4'
          onClick={handleStartReferring}
          type='button'
        >
          Start Referring Now
          <ArrowRightIcon className='w-4 h-4' />
        </button>
      </div>
      <div className='hidden md:block w-full md:w-1/2 order-1 md:order-2'>
        <Image
          alt='referral hero'
          className='w-full h-auto object-cover'
          height={600}
          src={'/temp/referrals/refer-cover.webp'}
          width={600}
        />
      </div>
    </section>
  );
};

export default ReferFriendHeroSection;
