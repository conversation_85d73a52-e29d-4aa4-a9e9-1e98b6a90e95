'use client';
import React, { useRef, useState } from 'react';
import Image from 'next/image';
import DateIcon from '../svg/date-icon';
import RoundCheck from '../svg/round-check';
import RightArrow from '../svg/right-arrow';
import { useAppDispatch } from '@/redux/hooks';
import {
  setClickId,
  setClickedDate,
  setClickedTime,
  setReportMissingStep,
  setSelectedStoreLogo,
} from '@/redux/slices/report-missing-cb-slice';
import dayjs from 'dayjs';
import clsx from 'clsx';
import { motion, AnimatePresence } from 'framer-motion';

export interface ReportMissingCbAccordianType {
  id: number;
  statusTracked: boolean;
}

export type TrackedTypes = 'Pending' | 'Confirmed' | 'Cancelled' | 'Tracked';

// const trackedTypesArray: TrackedTypes[] = [
//   'Pending',
//   'Confirmed',
//   'Cancelled',
//   'Tracked',
// ];

const ReportMissingCbAccordian = ({
  onClick,
  activeId,
  data,
}: {
  activeId: number;
  data: any;
  onClick: (id: number) => void;
}) => {
  const { uid: id } = data.store;
  const contentEl = useRef<HTMLDivElement>(null);

  return (
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      className='overflow-hidden shadow-sm'
      initial={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.5 }}
      whileHover={{ boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
    >
      <motion.div
        className='px-[10px] lg:px-[20px] flex justify-between items-center gap-[5px] h-[50px] lg:h-[60px] cursor-pointer font-medium rounded-[4px] bg-white dark:bg-[#3E424C] hover:bg-[#f9f9f9] dark:hover:bg-[#2D313A]'
        onClick={() => onClick(data?.store?.uid)}
        style={{ boxShadow: '0px 4px 16px 0px rgba(0, 0, 0, 0.04)' }}
        whileTap={{ scale: 0.98 }}
      >
        <motion.div
          animate={{ opacity: 1, x: 0 }}
          className='flex items-center'
          initial={{ opacity: 0, x: -10 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <OpenClose isOpen={activeId === id} />
          <motion.div
            className='relative w-[40px] h-[22px] lg:w-[66px] lg:h-[37px] mx-[10px]'
            transition={{ duration: 0.2 }}
            whileHover={{ scale: 1.1 }}
          >
            <Image
              alt='storeImg'
              className='object-contain'
              fill
              src={data?.store?.logo}
            />
          </motion.div>
          <span className='ml-[5px] text-[10px] lg:text-xs text-blackWhite font-medium'>
            {data?.store?.name}
          </span>
        </motion.div>
        <motion.div
          animate={{ opacity: 1, scale: 1 }}
          className='w-[35px] h-[35px] lg:w-[50px] lg:h-[50px] flex-center flex-col bg-[#FFC554] text-black rounded-[2.5px]'
          initial={{ opacity: 0, scale: 0.8 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          whileHover={{ scale: 1.1, rotate: 5 }}
        >
          <span className='text-[12px] lg:text-xs font-black'>
            {data?.store?.count}
          </span>
          <span className='text-[7px] lg:text-[10px] font-medium'>Clicks</span>
        </motion.div>
      </motion.div>
      <AnimatePresence>
        {activeId === id && (
          <motion.div
            animate={{
              height: 'auto',
              opacity: 1,
              transition: { duration: 0.3 },
            }}
            className='overflow-hidden'
            exit={{
              height: 0,
              opacity: 0,
              transition: { duration: 0.2 },
            }}
            initial={{ height: 0, opacity: 0 }}
            ref={contentEl}
          >
            <motion.div
              animate={{ opacity: 1 }}
              className='flex flex-col gap-y-[1px] mt-[1px]'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              {data?.store?.clicks?.map((item: any, index: number) => (
                <motion.div
                  animate={{ opacity: 1, y: 0 }}
                  initial={{ opacity: 0, y: 10 }}
                  key={item?.uid}
                  transition={{ duration: 0.2, delay: 0.1 + index * 0.05 }}
                >
                  <TrackItem
                    clickData={item}
                    fromPreviousMonth={item?.fromPreviousMonth}
                    status={item?.status}
                    storeLogo={data?.store?.logo}
                    // isStatusTracked={trackedTypesArray.includes(item?.status)}
                  />
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

const TrackItem = ({
  clickData,
  // isStatusTracked = true,
  storeLogo = '',
  status,
  fromPreviousMonth = false,
}: {
  isStatusTracked?: boolean;
  clickData?: any;
  storeLogo?: string;
  status?: string;
  fromPreviousMonth?: boolean;
}) => {
  const dispatch = useAppDispatch();
  const [showTooltip, setShowTooltip] = useState(false);

  // Store tooltip position
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, right: 0 });

  const handleClick = (e: React.MouseEvent) => {
    if (fromPreviousMonth) {
      e.stopPropagation();

      // Calculate position based on click event
      const rect = e.currentTarget.getBoundingClientRect();
      setTooltipPosition({
        top: rect.top - 40, // Position above the element
        right: window.innerWidth - rect.right + 20, // Align with right side
      });

      setShowTooltip(true);

      // Auto-hide tooltip after 3 seconds
      setTimeout(() => {
        setShowTooltip(false);
      }, 3000);
    } else if (status === 'Report Missing CB') {
      dispatch(setClickId(clickData?.id));
      dispatch(setSelectedStoreLogo(storeLogo));
      dispatch(setClickedDate(clickData?.date));
      dispatch(setClickedTime(clickData?.time));
      dispatch(setReportMissingStep(2));
    }
  };

  return (
    <motion.div
      className='px-[16px] lg:px-[20px] h-[52px] rounded-[4px] w-full flex items-center justify-between bg-[#F6F6F6] dark:bg-[#4F5460] relative hover:bg-[#f0f0f0] dark:hover:bg-[#565d6d]'
      onClick={handleClick}
      style={{ boxShadow: ' 0px 2px 4px 0px rgba(0, 0, 0, 0.08)' }}
      whileHover={{
        boxShadow: '0px 4px 8px 0px rgba(0, 0, 0, 0.12)',
      }}
      whileTap={{ scale: 0.98 }}
    >
      <motion.div
        animate={{ opacity: 1, x: 0 }}
        className='flex text-[10px] lg:text-xs'
        initial={{ opacity: 0, x: -5 }}
        transition={{ duration: 0.3 }}
      >
        <DateIcon className='w-[12px] lg:w-[16px] text-blackWhite' />
        <span className='font-semibold ml-[8px] text-blackWhite'>
          {dayjs(clickData?.date).format(`DD MMMM YYYY`)},
        </span>
        <span className='font-medium ml-[5px] text-[#999] dark:text-[#8C94A7]'>
          {dayjs(clickData?.time, 'HH:mm').format('hh:mm A')}
        </span>
      </motion.div>

      {status !== 'Report Missing CB' ? (
        <motion.div
          animate={{ opacity: 1, x: 0 }}
          className='flex'
          initial={{ opacity: 0, x: 5 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          {status === 'Tracked' && (
            <motion.div
              animate={{ scale: 1 }}
              initial={{ scale: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <RoundCheck className='w-[11px] lg:w-[15px] text-blackWhite' />
            </motion.div>
          )}

          <motion.span
            animate={{ opacity: 1 }}
            className={clsx(
              status === 'Pending' && 'text-[#E1B200]',
              status === 'Missing' &&
                'text-primary bg-[#EAEAEA] px-[8px] py-[6px] rounded-[5px] ml-[5px]',
              'text-[#407BFF] text-[10px] lg:text-sm font-semibold ml-[5px]'
            )}
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            {status}
          </motion.span>
        </motion.div>
      ) : (
        <motion.div
          animate={{ opacity: 1, x: 0 }}
          initial={{ opacity: 0, x: 5 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          whileHover={{ x: 5 }}
          whileTap={{ scale: 0.9 }}
        >
          <RightArrow className='w-[10px] lg:w-[16px] text-blackWhite cursor-pointer' />
        </motion.div>
      )}
      {/* Tooltip */}
      <AnimatePresence>
        {showTooltip && (
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className='fixed z-[9999] text-white text-xs px-3 py-2 border-[.6px] border-[#8c94a7] dark:bg-[#574ABE] light:bg-[#574ABE] rounded-lg'
            exit={{ opacity: 0, y: -10 }}
            initial={{ opacity: 0, y: 10 }}
            style={{
              pointerEvents: 'none',
              maxWidth: '200px',
              top: tooltipPosition.top,
              right: tooltipPosition.right,
            }}
            transition={{ duration: 0.2 }}
          >
            The window for reporting missing cashback for that order has closed.
            Claims must be submitted by the 4th of the following month.
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export const OpenClose = ({ isOpen }: { isOpen: boolean }) => {
  return (
    <motion.div
      animate={{ scale: 1, rotate: isOpen ? 0 : 0 }}
      className='w-[16px] h-[16px] rounded-full bg-primary text-white flex items-center justify-center text-[12px]'
      initial={{ scale: 0.8 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.2 }}
      whileTap={{ scale: 0.9 }}
    >
      <AnimatePresence mode='wait'>
        {isOpen ? (
          <motion.span
            animate={{ opacity: 1, rotate: 0 }}
            exit={{ opacity: 0, rotate: 90 }}
            initial={{ opacity: 0, rotate: -90 }}
            key='minus'
            transition={{ duration: 0.2 }}
          >
            -
          </motion.span>
        ) : (
          <motion.span
            animate={{ opacity: 1, rotate: 0 }}
            exit={{ opacity: 0, rotate: -90 }}
            initial={{ opacity: 0, rotate: 90 }}
            key='plus'
            transition={{ duration: 0.2 }}
          >
            +
          </motion.span>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default ReportMissingCbAccordian;
