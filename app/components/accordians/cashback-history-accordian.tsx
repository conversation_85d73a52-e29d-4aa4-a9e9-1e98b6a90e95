'use client';
import React, { useRef } from 'react';
import clsx from 'clsx';
import { ArrowDown, ArrowUp } from '../svg/arrow-up-down';
import Image from 'next/image';
import CashbackHandSVG from '../svg/cashback-hand';
import ApprovedSVG from '../svg/approved';
import PendingSVG from '../svg/pending';
import CancelledSVG from '../svg/cancelled';
import { formatIndRs } from '@/utils/helpers';
import dayjs from 'dayjs';
import { motion, AnimatePresence } from 'framer-motion';

export interface CbHistoryAccordianType {
  id: number;
  storeImgUrl: string;
  orderDate: string;
  cashbackAmount: number;
  orderAmount: number;
  RefId: string;
  remarks: string;
  status: 'confirmed' | 'pending' | 'cancelled';
}

const CbHistoryAccordian = ({
  onClick,
  data,
  activeId,
}: {
  activeId: number;
  data: CbHistoryAccordianType;
  onClick: (id: number) => void;
}) => {
  const {
    id,
    storeImgUrl,
    orderDate,
    orderAmount,
    cashbackAmount,
    remarks,
    RefId,
    status,
  } = data;
  const contentEl = useRef<HTMLDivElement>(null);

  return (
    <motion.div
      animate={{ opacity: 1 }}
      className='bg-[#F5F5F5] dark:bg-[#222529] rounded-[10px] overflow-hidden shadow-sm'
      initial={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
    >
      {/* ------------------------Accordion Header------------------------ */}
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className={clsx(
          activeId === id && '!rounded-t-[5px] !rounded-b-none',
          'flex justify-between items-center py-[7px] lg:py-[20px] ps-[15px] pr-[8px] lg:ps-[37px] lg:pr-[22px] cursor-pointer bg-container dark:bg-[#3E424C] rounded-[5px] border-[0.5px] border-white dark:border-[#353943]'
        )}
        initial={{ opacity: 0, y: -10 }}
        onClick={() => onClick(id)}
        style={{ boxShadow: '0px 4px 16px 0px rgba(0, 0, 0, 0.04)' }}
        transition={{ duration: 0.3, delay: 0.1 }}
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.99 }}
      >
        <motion.div
          animate={{ opacity: 1, x: 0 }}
          className='flex flex-col lg:flex-row lg:gap-x-[15px]'
          initial={{ opacity: 0, x: -10 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <motion.div
            animate={{ opacity: 1, scale: 1 }}
            className='relative w-[46px] h-[25px] lg:w-[67px] lg:h-[36px] overflow-hidden shrink-0'
            initial={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.3, delay: 0.3 }}
            whileHover={{ scale: 1.1 }}
          >
            <Image
              alt='store'
              className='object-contain'
              fill
              src={storeImgUrl}
            />
          </motion.div>
          <motion.div
            animate={{ opacity: 1 }}
            className='flex-center text-[6px] lg:text-[10px] text-[#828282] dark:text-[#B1BBD4] font-bold shrink-0 font-nexa lg:pt-[2px] mt-[2px]'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
          >
            {dayjs(orderDate).format('DD MMM YYYY hh:mm A')}
          </motion.div>
        </motion.div>

        <motion.div
          animate={{ opacity: 1, x: 0 }}
          className='flex items-center justify-end grow'
          initial={{ opacity: 0, x: 10 }}
          transition={{ duration: 0.3, delay: 0.5 }}
        >
          <EarningDetailCont
            amount={formatIndRs(cashbackAmount)}
            icon={<CashbackHandSVG className='w-[10px] lg:w-[15px]' />}
            title='Cashback'
          />
          <EarningDetailCont
            amount={formatIndRs(orderAmount)}
            icon={<CashbackHandSVG className='w-[10px] lg:w-[15px]' />}
            rootClass='!hidden lg:!flex-center ml-[9px]'
            title='Order value'
          />
          <CashbackStatusCont status={status} />

          <motion.div
            animate={{ opacity: 1 }}
            className='inline-block ml-[5%]'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.6 }}
            whileHover={{ scale: 1.2 }}
            whileTap={{ scale: 0.8 }}
          >
            <OpenClose isOpen={activeId === id} />
          </motion.div>
        </motion.div>
      </motion.div>

      {/* -------------------------Accordion Body-------------------------- */}
      <motion.div
        animate={{
          height:
            activeId === id ? contentEl.current?.scrollHeight || 'auto' : 0,
        }}
        className='overflow-hidden rounded-b-[10px]'
        initial={{ height: 0 }}
        ref={contentEl}
        transition={{ duration: 0.3, ease: [0.04, 0.62, 0.23, 0.98] }}
      >
        <motion.div
          animate={{ opacity: activeId === id ? 1 : 0 }}
          className='rounded-b-[5px] p-[16px] lg:py-[30px] text-[8px] sm:text-[9px] lg:text-xs text-black dark:!text-[#9A9A9A] font-normal text-justify'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <motion.div
            animate={{
              opacity: activeId === id ? 1 : 0,
              y: activeId === id ? 0 : 20,
            }}
            className='flex flex-col lg:flex-row lg:justify-center text-blackWhite'
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <motion.div
              animate={{ opacity: activeId === id ? 1 : 0 }}
              className='flex flex-col lg:flex-row w-full lg:w-auto'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
            >
              <motion.div
                animate={{
                  opacity: activeId === id ? 1 : 0,
                  x: activeId === id ? 0 : -10,
                }}
                className='flex gap-x-[5px] justify-between'
                initial={{ opacity: 0, x: -10 }}
                transition={{ duration: 0.3, delay: 0.4 }}
              >
                <motion.div
                  animate={{ opacity: activeId === id ? 1 : 0 }}
                  className='w-fit flex items-baseline justify-left lg:flex-col lg:hidden'
                  initial={{ opacity: 0 }}
                  transition={{ duration: 0.3, delay: 0.5 }}
                >
                  <span className='text-[10px] font-light'>Order Value</span>
                  <span className='text-[10px] font-nexa font-bold mt-[5px] ml-[10px] lg:ml-0'>
                    {formatIndRs(orderAmount)}
                  </span>
                </motion.div>
                <motion.div
                  animate={{
                    opacity: activeId === id ? 1 : 0,
                    scaleY: activeId === id ? 1 : 0,
                  }}
                  className='w-[1px] bg-[#BCBCBC] h-auto shrink-0 lg:hidden'
                  initial={{ opacity: 0, scaleY: 0 }}
                  transition={{ duration: 0.3, delay: 0.5 }}
                />
                <motion.div
                  animate={{ opacity: activeId === id ? 1 : 0 }}
                  className='w-fit flex items-baseline justify-left lg:flex-col lg:items-center'
                  initial={{ opacity: 0 }}
                  transition={{ duration: 0.3, delay: 0.6 }}
                >
                  <span className='text-[10px] lg:text-xs font-light'>
                    Ref. Id:
                  </span>
                  <span className='text-[10px] lg:text-xs font-nexa font-bold mt-[5px] ml-[10px] lg:ml-0'>
                    {RefId}
                  </span>
                </motion.div>
              </motion.div>
              <motion.div
                animate={{
                  opacity: activeId === id ? 1 : 0,
                  scaleY: activeId === id ? 1 : 0,
                }}
                className='w-[1px] bg-[#BCBCBC] h-auto shrink-0 lg:mx-[20px]'
                initial={{ opacity: 0, scaleY: 0 }}
                transition={{ duration: 0.3, delay: 0.6 }}
              />
              <motion.div
                animate={{
                  opacity: activeId === id ? 1 : 0,
                  x: activeId === id ? 0 : 10,
                }}
                className='flex items-center justify-left lg:flex-col mt-[10px] lg:mt-0'
                initial={{ opacity: 0, x: 10 }}
                transition={{ duration: 0.3, delay: 0.7 }}
              >
                <span className='text-[10px] lg:text-xs font-light'>
                  Approx. Confirmation Date
                </span>
                <span className='text-[10px] lg:text-xs font-nexa font-bold mt-[5px] ml-[10px] lg:ml-0'>
                  {dayjs(orderDate).format('DD MMM YYYY')}
                </span>
              </motion.div>
            </motion.div>
            <motion.div
              animate={{
                opacity: activeId === id ? 1 : 0,
                scaleY: activeId === id ? 1 : 0,
              }}
              className='w-[1px] bg-[#BCBCBC] h-auto shrink-0 hidden lg:block lg:mx-[20px]'
              initial={{ opacity: 0, scaleY: 0 }}
              transition={{ duration: 0.3, delay: 0.7 }}
            />
            <motion.div
              animate={{
                opacity: activeId === id ? 1 : 0,
                y: activeId === id ? 0 : 10,
              }}
              className='w-full pt-[10px] lg:pt-0 lg:w-auto flex items-start lg:items-center mt-[10px] lg:mt-0 justify-start border-t-[0.5px] border-[#E8E8E8] text-[10px] lg:text-xs lg:border-none font-light lg:flex-col'
              initial={{ opacity: 0, y: 10 }}
              transition={{ duration: 0.3, delay: 0.8 }}
            >
              <span className='font-medium lg:font-light mr-[4px]'>
                Remarks:
              </span>
              <span className='lg:mt-[5px] lg:font-normal'>{remarks}</span>
            </motion.div>
          </motion.div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

const EarningDetailCont = ({
  icon,
  title,
  amount,
  rootClass,
}: {
  icon: React.ReactNode;
  title: string;
  amount: string;
  rootClass?: string;
}) => {
  return (
    <motion.div
      animate={{ opacity: 1, scale: 1 }}
      className={clsx(
        rootClass,
        'flex-center bg-white dark:bg-[#31353D] rounded-[2.5px] h-[30px] lg:h-[53px] overflow-hidden'
      )}
      initial={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.05, boxShadow: '0px 4px 15px rgba(0, 0, 0, 0.1)' }}
    >
      <motion.div
        animate={{ opacity: 1 }}
        className='flex-center flex-col lg:bg-[#ECECEC] lg:dark:bg-[#4C515C] text-[8px] sm:text-[9px] lg:text-xs lg:w-[70px] h-full pl-[5px] text-blackWhite'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        {icon}
        <span className='hidden dark:text-white lg:inline-block text-[8px] truncate'>
          {title}
        </span>
      </motion.div>
      <motion.div
        animate={{ opacity: 1, x: 0 }}
        className='font-nexa dark:text-white text-[9px] lg:text-xs text-center lg:min-w-[100px] font-black px-[5px] lg:px-[9px] pt-[4px]'
        initial={{ opacity: 0, x: 10 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        {amount}
      </motion.div>
    </motion.div>
  );
};

const CashbackStatusCont = ({
  status,
}: {
  status: 'pending' | 'cancelled' | 'confirmed';
}) => {
  let color = 'text-[#3B9D88]';
  if (status === 'pending') {
    color = 'text-[#FFC554]';
  } else if (status === 'cancelled') {
    color = 'text-[#FF4141]';
  }
  return (
    <motion.div
      animate={{ opacity: 1, scale: 1 }}
      className='w-[80px] lg:w-[135px] h-[30px] lg:h-[53px] flex-center gap-x-[10px] bg-white dark:bg-[#31353D] rounded-[2.5px] ml-[5px] lg:bg-transparent'
      initial={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.05, boxShadow: '0px 4px 15px rgba(0, 0, 0, 0.1)' }}
    >
      <motion.div
        animate={{ opacity: 1, scale: 1 }}
        initial={{ opacity: 0, scale: 0 }}
        transition={{ duration: 0.3, delay: 0.1, type: 'spring' }}
      >
        {status === 'confirmed' ? (
          <ApprovedSVG className={clsx(color, 'w-[10px] lg:w-[16px]')} />
        ) : status === 'pending' ? (
          <PendingSVG className={clsx(color, 'w-[8px] lg:w-[14px]')} />
        ) : (
          <CancelledSVG className={clsx(color, 'w-[9px] lg:w-[15px]')} />
        )}
      </motion.div>
      <motion.span
        animate={{ opacity: 1, x: 0 }}
        className={clsx(color, 'text-[9px] lg:text-xs font-bold')}
        initial={{ opacity: 0, x: 10 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        {status}
      </motion.span>
    </motion.div>
  );
};

export const OpenClose = ({ isOpen }: { isOpen: boolean }) => {
  return (
    <>
      <AnimatePresence initial={false}>
        {isOpen ? (
          <motion.div
            animate={{ opacity: 1, rotate: 0 }}
            exit={{ opacity: 0, rotate: 180 }}
            initial={{ opacity: 0, rotate: 180 }}
            key='up'
            transition={{ duration: 0.2 }}
          >
            <ArrowUp className='text-[#929292] lg:w-[15px]' />
          </motion.div>
        ) : (
          <motion.div
            animate={{ opacity: 1, rotate: 0 }}
            exit={{ opacity: 0, rotate: -180 }}
            initial={{ opacity: 0, rotate: -180 }}
            key='down'
            transition={{ duration: 0.2 }}
          >
            <ArrowDown className='text-[#929292] lg:w-[15px]' />
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default CbHistoryAccordian;
