import React, { useEffect, useState } from 'react';
import { Radio, Space } from 'antd';
import BottomDrawer from '../atoms/BottomDrawer';
import PillButton, { SelectedPill } from '../atoms/pills';
// import { PercentageSliderFormatter } from '../atoms/percentage-slider-formatter';
import { MobileFilterProps } from '@/types/global-types';
import SearchSVG from '../svg/search';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import {
  useCreateMultiQueryString,
  useCreateQueryString,
} from '@/utils/custom-hooks';
import {
  setFilteredCategories,
  setSearchValueCat,
  setSelectedOfferType,
  setSelectedSubCategories,
  // setPercentageFilter,
  setSelectedUserType,
  setSelectedOnGoingSales,
  // setSelectedStoreStatusFilter,
} from '@/redux/slices/common-filters-slice';
import {
  AllSubCategory,
  OfferTypes,
  UserTypes,
} from '@/services/api/data-contracts';
import SearchInput, { SearchCategoriesMobile } from '../atoms/search-input';
import { toggleCategory } from '@/utils/helpers';
import ThemeButton from '../atoms/theme-btn';
import RightArrow from '../svg/right-arrow';

const CommonFilterMobile = ({
  isShowFilterModal,
  setShowFilterModal,
  filterProps,
}: MobileFilterProps) => {
  const pathname = usePathname();
  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const {
    selectedUserType,
    selectedOfferType,
    categories,
    filteredCategories,
    onGoingSalesList,
    searchValueCat,
    selectedSubCategories,
    selectedOnGoingSales,
    // percentageFilter,
  } = useAppSelector((state) => state.commonFilters);
  const [selectedMainCat, setSelectedMainCat] = useState<AllSubCategory>({
    uid: -1, //-1 for representing none of main-category is selected
    name: '',
  });
  const [showMore, setShowMore] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchInSelectedCat, setSearchInSelectedCat] = useState('');
  const dispatch = useAppDispatch();
  const createQueryString = useCreateQueryString(searchParams);
  const createMultiQueryString = useCreateMultiQueryString(searchParams);

  useEffect(() => {
    dispatch(
      setSelectedUserType(
        (searchParams.get('userType') as UserTypes) || UserTypes.Both
      )
    );
    dispatch(
      setSelectedOfferType(
        (searchParams.get('offerType') as OfferTypes) || 'deals'
      )
    );
  }, [searchParams, dispatch]);

  // const onChangePercentage = (value: {
  //   minPercent: number;
  //   maxPercent: number;
  // }) => {
  //   dispatch(setPercentageFilter(value));
  //   const queries = [
  //     { name: 'minPercent', value: value.minPercent.toString() },
  //     { name: 'maxPercent', value: value.maxPercent.toString() },
  //   ];

  //   const queryString = createMultiQueryString(queries);
  //   replace(pathname + '?' + queryString);
  // };

  const onChangeUser = ({ userType }: { userType: UserTypes }) => {
    dispatch(setSelectedUserType(userType));
    replace(pathname + '?' + createQueryString('userType', userType));
  };
  const onChangeOffer = ({ offerType }: { offerType: OfferTypes }) => {
    dispatch(setSelectedOfferType(offerType));
    replace(pathname + '?' + createQueryString('offerType', offerType));
  };

  const salesChangeHandler = ({ uid, name }: { uid: number; name: string }) => {
    const newSelectedOnGoingSales = toggleCategory({
      arr: selectedOnGoingSales,
      uid,
      name,
    });
    dispatch(setSelectedOnGoingSales(newSelectedOnGoingSales));

    const newSelectedOnGoingSalesString = newSelectedOnGoingSales
      .reduce<number[]>((total, item) => total.concat(item.uid), [])
      .join(',');
    replace(
      pathname + '?' + createQueryString('sales', newSelectedOnGoingSalesString)
    );
  };

  const subCategoryHandler = ({ uid, name }: { uid: number; name: string }) => {
    const newSelectedSubCategories = toggleCategory({
      arr: selectedSubCategories,
      uid,
      name,
    });
    dispatch(setSelectedSubCategories(newSelectedSubCategories));

    const newSelectedSubCategoriesString = newSelectedSubCategories
      .reduce<number[]>((total, item) => total.concat(item.uid), [])
      .join(',');

    replace(
      pathname +
        '?' +
        createQueryString('subCategories', newSelectedSubCategoriesString)
    );
  };

  useEffect(() => {
    //when search trigger after selected main category then trying to filter sub-categories only in selected main category
    if (searchInSelectedCat) {
      const mainCategory = categories.find(
        (category) => category.uid === selectedMainCat.uid
      );

      const filteredSubcategories = mainCategory
        ? mainCategory.subCategories.filter((subCategory) =>
            subCategory.name
              .toLowerCase()
              .includes(searchInSelectedCat.toLowerCase())
          )
        : [];

      dispatch(
        setFilteredCategories([
          {
            uid: selectedMainCat.uid,
            name: selectedMainCat.name,
            subCategories: filteredSubcategories,
          },
        ])
      );
    } else {
      dispatch(setFilteredCategories(categories));
    }
  }, [searchInSelectedCat, categories, dispatch, selectedMainCat]);

  const handleSelectedCatClose = () => {
    if (searchInSelectedCat) {
      setSearchInSelectedCat('');
    } else {
      setSearchOpen(false);
    }
  };

  const handleRestAllFilters = () => {
    setFilteredCategories(categories);
    setSelectedMainCat({ uid: -1, name: '' });
    setSearchInSelectedCat('');
    dispatch(setSearchValueCat(''));
    dispatch(setSelectedUserType(UserTypes.Both));
    //passing empty string value to createMultiQueryString will delete query params
    replace(
      pathname +
        '?' +
        createMultiQueryString([
          { name: 'page', value: '' },
          { name: 'pageSize', value: '' },
          { name: 'userType', value: '' },
          { name: 'offerType', value: '' },
          { name: 'subCategories', value: '' },
        ])
    );
  };

  //  const onChangeStoreStatus = ({ storeStatus }: { storeStatus : string}) => {
  //     dispatch(setSelectedStoreStatusFilter(storeStatus));
  //     replace(pathname + '?' + createQueryString('offerType', offerType));
  //   };

  return (
    <BottomDrawer
      heightClass='75svh'
      onClose={setShowFilterModal}
      open={isShowFilterModal}
      topClass='calc(100% - 75svh)'
    >
      <div className='pt-[10px]'>
        {/* --------------------Hidden for phase 1============== */}
        {/* {filterProps.some((item) => item.filter === 'percentage') && (
          <>
            <span className='text-[10px] text-blackWhite2 font-normal pl-[8px]'>
              Percentage
            </span>
            <Slider
              className='mt-[50px] mx-[20px]'
              classNames={{
                track: '!bg-[#7366D9]',
                rail: '!bg-[#E7E9EB] dark:!bg-black',
              }}
              max={100}
              min={0}
              onChange={(value) =>
                onChangePercentage({
                  minPercent: value[0],
                  maxPercent: value[1],
                })
              }
              range
              tooltip={{ formatter: PercentageSliderFormatter }}
              value={[percentageFilter.minPercent, percentageFilter.maxPercent]}
            />
          </>
        )} */}
        {selectedOnGoingSales.length > 0 &&
          filterProps.some((item) => item.filter === 'sale') && (
            <>
              <h4 className='pl-[8px] mb-[20px] text-blackWhite text-xs font-semibold'>
                Sales
              </h4>
              {
                <div className='flex flex-wrap mt-[13px] gap-y-[8px] gap-x-[10px]'>
                  {selectedOnGoingSales.map((item) => (
                    <SelectedPill
                      key={item.uid}
                      onClick={() => {
                        const newSelectedUids = selectedOnGoingSales.filter(
                          (itm) => itm.uid !== item.uid
                        );
                        dispatch(setSelectedOnGoingSales(newSelectedUids));
                        const newSelectedOnGoingSalesString = newSelectedUids
                          .reduce<number[]>(
                            (total, item) => total.concat(item.uid),
                            []
                          )
                          .join(',');

                        replace(
                          pathname +
                            '?' +
                            createQueryString(
                              'sales',
                              newSelectedOnGoingSalesString
                            )
                        );
                      }}
                      text={item.name}
                    />
                  ))}
                </div>
              }
              <div className='flex flex-wrap mt-[30px] overflow-hidden pb-[3px] gap-x-[8px] gap-y-[10px]'>
                {(onGoingSalesList.length > 5 && !showMore
                  ? onGoingSalesList.slice(0, 5)
                  : onGoingSalesList
                ).map((item) => (
                  <PillButton
                    className='border-[1px] border-primary dark:text-white w-max'
                    isSelected={selectedOnGoingSales.some(
                      (itm) => itm.uid === item.uid
                    )}
                    key={item.uid}
                    onClick={() =>
                      salesChangeHandler({ uid: item.uid, name: item.name })
                    }
                    text={item.name}
                  />
                ))}

                <PillButton
                  className='border-[1px] border-primary dark:text-white w-max'
                  isSelected={true}
                  onClick={() => {
                    setShowMore(!showMore);
                  }}
                  text={
                    showMore
                      ? 'Show Less'
                      : `+ ${onGoingSalesList.length - 5} More`
                  }
                />
              </div>
            </>
          )}
        {filterProps.some((item) => item.filter === 'user') && (
          <>
            <h4 className='pl-[8px] mb-[20px] text-blackWhite text-xs font-semibold'>
              User Type
            </h4>
            <Radio.Group
              className='pl-[8px]'
              onChange={(e) => onChangeUser({ userType: e.target.value })}
              value={selectedUserType}
            >
              <Space direction='vertical'>
                <Radio
                  className='text-xs font-normal text-black dark:text-white'
                  defaultChecked
                  value={UserTypes.Both}
                >
                  Both New and Old Users
                </Radio>
                <Radio
                  className='text-xs font-normal text-black dark:text-white'
                  value={UserTypes.New}
                >
                  New User
                </Radio>
                <Radio
                  className='text-xs font-normal text-black dark:text-white'
                  value={UserTypes.Existing}
                >
                  Old User
                </Radio>
              </Space>
            </Radio.Group>
          </>
        )}
        {filterProps.some((item) => item.filter === 'offer') && (
          <>
            <h4 className='mt-[30px] mb-[20px] text-blackWhite text-xs font-semibold pl-[8px]'>
              Offer Type
            </h4>
            <Radio.Group
              className='pl-[8px]'
              onChange={(e) => onChangeOffer({ offerType: e.target.value })}
              value={selectedOfferType}
            >
              <Space direction='vertical'>
                <Radio
                  className='text-xs text-black dark:text-white'
                  defaultChecked
                  value={'deals'}
                >
                  All Deals
                </Radio>
                <Radio
                  className='text-xs text-black dark:text-white'
                  value={'coupons'}
                >
                  Coupons
                </Radio>
                <Radio
                  className='text-xs text-black dark:text-white'
                  value={'trending'}
                >
                  Trending
                </Radio>    
                {/*<Radio className='text-xs text-blackWhite' value={'deals'}>
                  Deals
                </Radio>*/}
              </Space>
            </Radio.Group>
          </>
        )}
        {selectedSubCategories.length > 0 && (
          <>
            <h4 className='text-[10px] text-blackWhite mt-[20px]'>Selected</h4>
            <div className='flex flex-wrap mt-[13px] gap-y-[8px] gap-x-[10px]'>
              {selectedSubCategories.map((item) => (
                <SelectedPill
                  key={item.uid}
                  onClick={() => {
                    const newSelectedUids = selectedSubCategories.filter(
                      (itm) => itm.uid !== item.uid
                    );
                    dispatch(setSelectedSubCategories(newSelectedUids));
                    const newSelectedSubCategoriesString = newSelectedUids
                      .reduce<number[]>(
                        (total, item) => total.concat(item.uid),
                        []
                      )
                      .join(',');

                    replace(
                      pathname +
                        '?' +
                        createQueryString(
                          'subCategories',
                          newSelectedSubCategoriesString
                        )
                    );
                  }}
                  text={item.name}
                />
              ))}
            </div>
          </>
        )}

        {searchOpen ? (
          //when main-category is selected, this is to search only inside selected main-category
          selectedMainCat.uid !== -1 ? (
            <SearchCategoriesMobile
              OnClickCross={handleSelectedCatClose}
              onChange={(value) => setSearchInSelectedCat(value)}
              selectedCategory={selectedMainCat.name}
              value={searchInSelectedCat}
            />
          ) : (
            //when no main-category is selected, this is to search sub-categories inside all main-categories
            <div className='flex-center mt-[25px]'>
              <RightArrow
                className='w-[16px] rotate-[180deg] text-primary mr-[15px]'
                onClick={() => {
                  setSearchOpen(false);
                  dispatch(setSearchValueCat(''));
                }}
              />
              <SearchInput
                onChange={(value) => dispatch(setSearchValueCat(value))}
                onClose={() => dispatch(setSearchValueCat(''))}
                placeholder='Search Sub categories...'
                value={searchValueCat}
              />
            </div>
          )
        ) : (
          <>
            {/* ---------------Category title bar with search icon------------- */}
            <div className='flex items-center mt-[20px]'>
              {selectedMainCat.uid !== -1 && (
                <RightArrow
                  className='w-[16px] rotate-[180deg] text-primary mr-[15px]'
                  onClick={() =>
                    setSelectedMainCat({
                      uid: -1,
                      name: '',
                    })
                  }
                />
              )}
              <h4 className='text-[10px] text-blackWhite2 font-semibold'>
                Category
              </h4>
              {selectedMainCat.uid !== -1 && (
                <span className='pl-[2px] text-[10px] font-bold'>
                  : {selectedMainCat.name}
                </span>
              )}
              <div className='grow h-[0.5px] bg-[#C2BAFF] mx-[10px]' />
              <SearchSVG
                className='text-[14px] text-primary'
                onClick={() => setSearchOpen(true)}
              />
            </div>
          </>
        )}

        <div className='flex flex-wrap mt-[30px] overflow-hidden pb-[3px] gap-x-[8px] gap-y-[10px]'>
          {searchValueCat || searchInSelectedCat ? (
            <>
              {/* -----------------------Search Results--------------- */}
              {filteredCategories.map((item) =>
                item.subCategories.map((item) => (
                  <PillButton
                    className='border-[1px] border-primary dark:text-white w-max'
                    isSelected={selectedSubCategories.some(
                      (itm) => itm.uid === item.uid
                    )}
                    key={item.uid}
                    onClick={() =>
                      subCategoryHandler({ uid: item.uid, name: item.name })
                    }
                    text={item.name}
                  />
                ))
              )}
            </>
          ) : (
            <>
              {/* --------------original categories and sub categories--------- */}
              {selectedMainCat.uid === -1
                ? categories.map((item) => (
                    <PillButton
                      className='border-[1px] border-primary dark:text-white w-max'
                      key={item.uid}
                      onClick={() =>
                        setSelectedMainCat({ uid: item.uid, name: item.name })
                      }
                      text={item.name}
                      useArrow
                    />
                  ))
                : categories
                    .find((category) => category.uid === selectedMainCat.uid)
                    ?.subCategories.map((item) => (
                      <PillButton
                        className='border-[1px] border-primary dark:text-white w-max'
                        isSelected={selectedSubCategories.some(
                          (itm) => itm.uid === item.uid
                        )}
                        key={item.uid}
                        onClick={() =>
                          subCategoryHandler({ uid: item.uid, name: item.name })
                        }
                        text={item.name}
                      />
                    ))}
            </>
          )}
        </div>

        <div className='sticky bottom-[-1px] h-[60px]  bg-container flex-center gap-x-[30px] mt-[40px] z-[9] w-full'>
          <ThemeButton
            className='!w-[100px] !text-[12px]'
            onClick={handleRestAllFilters}
            text='Reset All'
          />
        </div>
      </div>
    </BottomDrawer>
  );
};

export default CommonFilterMobile;
