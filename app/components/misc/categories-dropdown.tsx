import React from 'react';
import { ArrowUp } from '../svg/arrow-up-down';
import Image from 'next/image';
import clsx from 'clsx';
import CategoryCard from '../cards/category-card';
import StoreByCBCard from '../landing/stores-cb-percentage/store-by-cb-card';
import ThemeButton from '../atoms/theme-btn';
import EnhancedNoData from '../enhanced-no-data';
import type {
  CategoryResponse,
  SubCategoriesByCategoryResponse,
} from '@/services/api/data-contracts';
import type { PromiseStatus } from '@/types/global-types';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { setActiveCategory } from '@/redux/slices/categories-list-slice';
import { useRouter } from 'next/navigation';
import { formatStoreName, generateProductUrl } from '@/utils/helpers';
import { LoadingGif } from './loading-components';

const CategoriesDropdown = ({
  data,
  promiseStatus,
  rootClass,
}: {
  data: {
    categories: CategoryResponse[];
    subCategories: SubCategoriesByCategoryResponse;
  };
  promiseStatus: PromiseStatus;
  rootClass: string;
}) => {
  const dispatch = useAppDispatch();
  const { activeCategory, subCategoriesList, isActiveCategoryLoading } =
    useAppSelector((state) => state.categoriesList);
  const router = useRouter();

  const subCategories = activeCategory
    ? subCategoriesList[activeCategory]?.subCategories
    : data?.subCategories?.subCategories;

  const stores = activeCategory
    ? subCategoriesList[activeCategory]?.stores
    : data?.subCategories?.stores;

  if (promiseStatus === 'rejected') {
    return;
  }

  const handleSubCategoryRedirect = (uid: number) => {
    router.push(`/deals-and-coupons?subCategories=${uid}`);
  };

  const handleOfferRedirect = (
    storeName: string,
    offerTitle: string,
    uid: number
  ) => {
    const url = generateProductUrl(storeName, offerTitle);
    router.push(`/store/${formatStoreName(storeName)}/${url}?uid=${uid}`);
  };

  return (
    <div
      className={clsx(
        rootClass,
        'header-container group-hover:shadow-md w-full transition-all duration-[400ms] absolute top-[41px] left-0 h-[473px] translate-y-[-473px] z-[-1] rounded-b-[10px] '
      )}
    >
      <div className='bg-[#F2F2F2] dark:bg-[#3A3F49] flex'>
        {/* Categories sidebar - make it responsive but maintain minimum width */}
        <div className='h-[473px] overflow-auto overscroll-contain w-[230px] 2xl:w-[320px] flex-shrink-0 bg-white dark:bg-[#282B31] shadow-sm pb-[18px] inline-block pt-5'>
          {data.categories.length > 0 ? (
            data.categories.map((item) => (
              <div
                className={clsx(
                  activeCategory === item.id &&
                    'bg-[#F2F2F2] dark:bg-[#3A3F49]',
                  'h-[45px] w-full text-blackWhite pl-[15px] pr-[12px] flex justify-between items-center gap-x-[5px] cursor-pointer transition-all duration-300 hover:pl-[18px]'
                )}
                key={item?.id}
                onMouseEnter={() => dispatch(setActiveCategory(item?.id))}
              >
                <div className='flex-center'>
                  <div className='w-[25px] flex-shrink-0'>
                    <Image
                      alt='icon'
                      className='w-5 h-5 object-scale-down'
                      height={25}
                      quality={100}
                      src={item.iconUrl}
                      width={25}
                    />
                  </div>
                  <p className='text-[10px] xl:text-xs 2xl:text-sm ml-[10px] text-left font-semibold maxLines1'>
                    {item.name}
                  </p>
                </div>
                <ArrowUp className='text-blackWhite rotate-90 flex-shrink-0' />
              </div>
            ))
          ) : (
            <EnhancedNoData
              customHeight='h-full'
              imgClass='!w-[100px] !h-[100px]'
              message='No categories available'
              showHomeLink={false}
            />
          )}
        </div>

        {/* Main content area - make it fully responsive */}
        <div className='h-[473px] flex grow overflow-hidden'>
          {/* Sub-categories section - responsive width */}
          <div className='h-[473px] w-[250px] lg:w-[320px] xl:w-[400px] 2xl:w-[480px] py-[30px] overflow-auto overscroll-contain flex-shrink-0'>
            <h4 className='text-blackWhite text-xs xl:text-sm font-semibold ml-[20px]'>
              Top Sub-categories
            </h4>
            {!isActiveCategoryLoading ? (
              subCategories?.length > 0 ? (
                <div className='mt-[22px] grid grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-[8px] px-[20px]'>
                  {subCategories?.map((item) => (
                    <div
                      className='transition-all duration-300 hover:scale-105'
                      key={item.uid}
                    >
                      <CategoryCard
                        imgUrl={item.iconUrl}
                        isLiked
                        onClickCard={() => handleSubCategoryRedirect(item.uid)}
                        text={item.title}
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <EnhancedNoData
                  customHeight='h-[400px]'
                  imgClass='!w-[150px] !h-[150px]'
                  message='No sub-categories available'
                  showHomeLink={false}
                />
              )
            ) : (
              <LoadingGif className='mt-[30px] lg:mt-[40px]' />
            )}
          </div>

          {/* Stores section - responsive width */}
          <div className='min-w-[300px] lg:min-w-[346px] h-[473px] grow bg-white dark:bg-container min-h-full py-[30px] overflow-auto overscroll-contain'>
            <h4 className='text-blackWhite text-xs xl:text-sm font-semibold ml-[15px]'>
              Top Stores
            </h4>
            {!isActiveCategoryLoading ? (
              stores?.length > 0 ? (
                <div className='mt-[22px] grid grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 3xl:grid-cols-5 px-[10px] gap-y-[5px] gap-x-[8px] justify-items-center'>
                  {stores?.map((item) => (
                    <div
                      className='transition-all duration-300 hover:scale-105 w-full'
                      key={item.uid}
                    >
                      <StoreByCBCard
                        bgColor={item.bgColor}
                        caption={item.caption}
                        className='w-full max-w-[146px]'
                        saved={item.saved}
                        src={item.imageUrl}
                        storeName={item.storeName}
                        uid={item.uid}
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <EnhancedNoData
                  customHeight='h-[400px]'
                  imgClass='!w-[150px] !h-[150px]'
                  message='No stores available'
                  showHomeLink={false}
                />
              )
            ) : (
              <LoadingGif className='mt-[30px] lg:mt-[40px]' />
            )}
          </div>

          {/* Latest offer section - show only on larger screens */}
          {!isActiveCategoryLoading ? (
            subCategoriesList[activeCategory]?.latestOffer ? (
              <div className='hidden xl:flex-center w-[200px] 2xl:w-[250px] flex-shrink-0 h-[473px] overflow-auto overscroll-contain'>
                <div className='w-full px-4 text-center text-blackWhite'>
                  <button
                    className='transition-all duration-300 hover:scale-105 block w-full'
                    onClick={() =>
                      handleOfferRedirect(
                        subCategoriesList[activeCategory].latestOffer.storeName,
                        subCategoriesList[activeCategory].latestOffer
                          .offerTitle,
                        subCategoriesList[activeCategory].latestOffer.uid
                      )
                    }
                    type='button'
                  >
                    <Image
                      alt=''
                      className='max-w-[150px] max-h-[150px] w-full h-auto object-contain rounded-[10px] mx-auto'
                      height={150}
                      src={
                        subCategoriesList[activeCategory].latestOffer.offerImage
                      }
                      width={150}
                    />
                  </button>
                  <p className='maxLines2 text-sm font-medium mt-4'>
                    {subCategoriesList[activeCategory].latestOffer.storeName}
                  </p>
                  <p className='maxLines3 text-xs font-medium my-2'>
                    {subCategoriesList[activeCategory].latestOffer.offerTitle}
                  </p>
                  <ThemeButton
                    className='!w-full max-w-[146px] mt-[7px] transition-all duration-300 hover:scale-105 mx-auto'
                    onClick={() => console.log('')}
                    text='GRAB DEAL'
                  />
                </div>
              </div>
            ) : (
              <div className='hidden xl:block w-[200px] 2xl:w-[250px] flex-shrink-0'>
                <EnhancedNoData
                  customHeight='h-[473px]'
                  imgClass='!w-[120px] !h-[120px]'
                  message='No latest offers'
                  showHomeLink={false}
                />
              </div>
            )
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default CategoriesDropdown;
