import Image from 'next/image';
import React, { useState } from 'react';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { useDispatch } from 'react-redux';
import { motion } from 'framer-motion';
import {
  setClickId,
  setClickedDate,
  setClickedTime,
  setReportMissingStep,
  setSelectedStoreLogo,
} from '@/redux/slices/report-missing-cb-slice';
import { useRouter } from 'next/navigation';

//TODO - change to swagger type later
export type ClickTypeStatusEnumTemp =
  | 'Pending'
  | 'Confirmed'
  | 'Cancelled'
  | 'Tracked'
  | 'Missing'
  | 'Report Missing CB';

const ClickHistoryComponent = ({
  clickId,
  date,
  storeLogo,
  name,
  status,
  time,
  clickDocId,
  fromPreviousMonth = false,
}: {
  clickId: string;
  date: string;
  storeLogo: string;
  name: string;
  status: ClickTypeStatusEnumTemp;
  time: string;
  clickDocId: string;
  fromPreviousMonth?: boolean;
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [showTooltip, setShowTooltip] = useState(false);

  // Store tooltip position
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, right: 0 });

  const handleClick = (e: React.MouseEvent) => {
    if (fromPreviousMonth) {
      e.stopPropagation();

      // Calculate position based on click event
      const rect = e.currentTarget.getBoundingClientRect();
      setTooltipPosition({
        top: rect.top - 40, // Position above the element
        right: window.innerWidth - rect.right + 20, // Align with right side
      });

      setShowTooltip(true);

      // Auto-hide tooltip after 3 seconds
      setTimeout(() => {
        setShowTooltip(false);
      }, 3000);
    } else {
      dispatch(setClickId(clickDocId));
      dispatch(setReportMissingStep(2));
      dispatch(setSelectedStoreLogo(storeLogo));
      dispatch(setClickedDate(date));
      dispatch(setClickedTime(time));
      router.push('/report-missing-cashback');
    }
  };

  return (
    <motion.div
      animate={{ opacity: 1 }}
      className='rounded-[5px] flex flex-col overflow-hidden relative'
      initial={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
    >
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className='bg-container h-[45px] lg:h-[50px] flex items-center justify-evenly lg:justify-start overflow-hidden border-b-[0.5px] border-[#e2e2e227] gap-x-[10px] px-[10px] lg:pl-[40px]'
        initial={{ opacity: 0, y: -10 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <motion.div
          animate={{ opacity: 1, scale: 1 }}
          className='w-[46px] h-[40px] lg:w-[105px] lg:h-[50px] relative shrink-0'
          initial={{ opacity: 0, scale: 0.8 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Image alt='...' className='object-contain' fill src={storeLogo} />
        </motion.div>
        <motion.div
          animate={{ opacity: 1, x: 0 }}
          className='text-[10px] lg:text-xs text-blackWhite min-w-0 flex lg:ml-[35px]'
          initial={{ opacity: 0, x: 20 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <span className='shrink-0'>Item:</span>
          <p className='font-medium ml-[4px] lg:ml-[10px] truncate'>{name}</p>
        </motion.div>
      </motion.div>
      <motion.div
        animate={{ opacity: 1 }}
        className='bg-white dark:bg-[#3E424C] h-[57px] flex items-center justify-evenly text-blackWhite border-b-[0.5px] border-[#b3b2b24e] dark:border-[#e2e2e227]'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.4 }}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='flex flex-col'
          initial={{ opacity: 0, y: 10 }}
          transition={{ duration: 0.3, delay: 0.5 }}
        >
          <span className='text-[10px] lg:text-[10px] font-light'>
            Click ID:
          </span>
          <span className='text-[10px] lg:text-xs font-bold mt-[5px]'>
            {clickId}
          </span>
        </motion.div>
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='flex flex-col'
          initial={{ opacity: 0, y: 10 }}
          transition={{ duration: 0.3, delay: 0.6 }}
        >
          <span className='text-[10px] lg:text-[10px] font-light'>Date:</span>
          <span className='text-[10px] lg:text-xs font-bold mt-[5px]'>
            {dayjs(date).format('DD MMM YYYY').toUpperCase()}
            {/* 12 JUN 2023*/}
          </span>
        </motion.div>
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='flex flex-col'
          initial={{ opacity: 0, y: 10 }}
          transition={{ duration: 0.3, delay: 0.7 }}
        >
          <span className='text-[10px] lg:text-[10px] font-light'>Time:</span>
          <span className='text-[10px] lg:text-xs font-bold mt-[5px]'>
            {/*12:55 PM*/}
            {dayjs(time, 'HH:mm').format('hh:mm A')}
          </span>
        </motion.div>
      </motion.div>
      <motion.div
        animate={{ opacity: 1 }}
        className='h-[40px] lg:h-[50px] bg-white dark:bg-[#3E424C] flex items-center justify-end pr-[20px] lg:pr-[40px]'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.8 }}
      >
        {status === 'Report Missing CB' ? (
          <motion.button
            className='focus:outline-none'
            onClick={handleClick}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <span
              className={clsx(
                'text-primary bg-[#EAEAEA] px-[8px] py-[6px] rounded-[5px]',
                'text-[#407BFF] text-[10px] lg:text-sm font-semibold'
              )}
            >
              Report Missing CB
            </span>
          </motion.button>
        ) : (
          <span
            className={clsx(
              status === 'Pending' && 'text-[#E1B200]',
              status === 'Missing' &&
                'text-primary bg-[#EAEAEA] px-[8px] py-[6px] rounded-[5px]',
              'text-[#407BFF] text-[10px] lg:text-sm font-semibold'
            )}
          >
            {status}
          </span>
        )}
      </motion.div>

      {/* Tooltip */}
      {showTooltip && (
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='fixed z-[9999] text-white text-xs px-3 py-2 border-[.6px] border-[#8c94a7] bg-[#574ABE] dark:bg-[#574ABE] rounded-lg'
          exit={{ opacity: 0, y: -10 }}
          initial={{ opacity: 0, y: 10 }}
          style={{
            pointerEvents: 'none',
            maxWidth: '200px',
            top: tooltipPosition.top,
            right: tooltipPosition.right,
          }}
          transition={{ duration: 0.2 }}
        >
          The window for reporting missing cashback for that order has closed.
          Claims must be submitted by the 4th of the following month.
        </motion.div>
      )}
    </motion.div>
  );
};

export default ClickHistoryComponent;
