'use client';
import clsx from 'clsx';
import { useRouter } from 'next/navigation';
import CashbackHistory from '@/app/components/svg/cashback-history';
import ClickHistorySVG from '@/app/components/svg/click-history-icon';
import OverviewSVG from '@/app/components/svg/overview-icon';
import PaymentSVG, { PaymentHistory } from '@/app/components/svg/payment-icon';
import { motion } from 'framer-motion';
// import RedeemGiftcard from '@/app/components/svg/redeem-giftcard';
// import { RedeemHistory } from '@/app/components/svg/redeem-icb-giftcard';
import ReferFriendSVG, {
  ReferralHistorySVG,
} from '@/app/components/svg/refer-freind';
import ReportMissing, {
  ReportMissingHistory,
} from '@/app/components/svg/report-missing';

const MyEarningsSidenav = ({ activeNavId }: { activeNavId: number }) => {
  const router = useRouter();

  return (
    <motion.aside
      animate={{ opacity: 1, x: 0 }}
      className='bg-container shadow-md w-[73px] lg:w-[271px] h-[calc(100vh-65px)] lg:h-[calc(100vh-146px)] shrink-0 grow-0 sticky top-[65px] lg:top-[146px] left-0 z-[5] overscroll-contain overflow-auto pb-[9rem]'
      initial={{ opacity: 0, x: -50 }}
      style={{ boxShadow: '3px -8px 5px rgb(0 0 0 / 15%)' }}
      transition={{ duration: 0.5 }}
    >
      {[
        {
          id: 1,
          title: 'Overview',
          icon: (
            <OverviewSVG
              className={clsx(
                activeNavId === 1 && '!text-white',
                'w-[14px] lg:w-[21px] text-primary group-hover:!text-black'
              )}
            />
          ),
          redirect: '/my-earnings-overview',
        },
        {
          id: 2,
          title: 'Cashback History',
          icon: (
            <CashbackHistory
              className={clsx(
                activeNavId === 2 && '!text-white',
                'w-[18px] lg:w-[27px] text-primary group-hover:!text-black'
              )}
            />
          ),
          redirect: '/cashback-history',
        },
        {
          id: 3,
          title: 'Payments',
          icon: (
            <PaymentSVG
              className={clsx(
                activeNavId === 3 && '!text-white',
                'w-[18px] lg:w-[27px] text-primary group-hover:!text-black'
              )}
            />
          ),
          redirect: '/payments',
        },
        {
          id: 4,
          title: 'Payment History',
          icon: (
            <PaymentHistory
              className={clsx(
                activeNavId === 4 && '!text-white',
                'w-[18px] lg:w-[27px] text-primary group-hover:!text-black'
              )}
            />
          ),
          redirect: '/payment-history',
        },
        {
          id: 5,
          title: 'Report Missing Cashback',
          icon: (
            <ReportMissing
              className={clsx(
                activeNavId === 5 && '!text-white',
                'w-[18px] lg:w-[27px] text-primary group-hover:!text-black'
              )}
            />
          ),
          redirect: '/report-missing-cashback',
        },
        {
          id: 6,
          title: 'Report Missing CB History',
          icon: (
            <ReportMissingHistory
              className={clsx(
                activeNavId === 6 && '!text-white',
                'w-[18px] lg:w-[27px] text-primary group-hover:!text-black'
              )}
            />
          ),
          redirect: '/report-missing-cashback-history',
        },
        {
          id: 7,
          title: 'Click History',
          icon: (
            <ClickHistorySVG
              className={clsx(
                activeNavId === 7 && '!text-white',
                'w-[12px] lg:w-[18px] text-primary group-hover:!text-black'
              )}
            />
          ),
          redirect: '/click-history',
        },
        {
          id: 8,
          title: 'Refer Friends',
          icon: (
            <ReferFriendSVG
              className={clsx(
                activeNavId === 8 && '!text-white',
                'w-[18px] lg:w-[27px] text-primary group-hover:!text-black'
              )}
            />
          ),
          redirect: '/refer-friends',
        },
        {
          id: 9,
          title: 'Referral Histoy',
          icon: (
            <ReferralHistorySVG
              className={clsx(
                activeNavId === 9 && '!text-white',
                'w-[14px] lg:w-[27px] text-primary group-hover:!text-black'
              )}
            />
          ),
          redirect: '/referral-history',
        },
        // {
        //   id: 10,
        //   title: 'Redeem ICB Giftcard',
        //   icon: (
        //     <RedeemGiftcard
        //       className={clsx(
        //         activeNavId === 10 && '!text-white',
        //         'w-[15px] lg:w-[23px] text-primary group-hover:!text-black'
        //       )}
        //     />
        //   ),
        //   redirect: '/redeem-icb-giftcard',
        // },
        // {
        //   id: 11,
        //   title: 'Redeem History',
        //   icon: (
        //     <RedeemHistory
        //       className={clsx(
        //         activeNavId === 11 && '!text-white',
        //         'w-[15px] lg:w-[23px] text-primary group-hover:!text-black'
        //       )}
        //     />
        //   ),
        //   redirect: '/redeem-history',
        // },
      ].map((item) => (
        <motion.div
          animate={{ opacity: 1, x: 0 }}
          className={clsx(
            item.id === activeNavId && '!bg-primary',
            'min-h-[74px] w-full bg-container dark:bg-[#3E424C] px-[12px] py-[8px] mb-[2px] flex-center flex-col items-center lg:flex-row gap-y-[8px] lg:gap-x-[15px] lg:justify-start lg:ps-[24px] shadow cursor-pointer group hover:!bg-[#FFC554]'
          )}
          initial={{ opacity: 0, x: -30 }}
          key={item?.id}
          onClick={() => {
            router.push(item?.redirect || '');
            router.refresh();
          }}
          transition={{ duration: 0.3, delay: 0.1 * item.id }}
          whileHover={{ scale: 1.03, x: 5 }}
          whileTap={{ scale: 0.97 }}
        >
          <motion.div
            animate={{ opacity: 1, scale: 1 }}
            className='w-[30px] shrink-0 flex-center !text-black'
            initial={{ opacity: 0, scale: 0.5 }}
            transition={{ duration: 0.3, delay: 0.1 * item.id + 0.1 }}
            whileHover={{ rotate: 10 }}
          >
            {item.icon}
          </motion.div>
          <motion.p
            animate={{ opacity: 1 }}
            className={clsx(
              item.id === activeNavId && '!text-white ',
              'text-[8px] sm:text-[9px] lg:text-xs text-center font-semibold text-blackWhite maxLines3 group-hover:!text-black'
            )}
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.1 * item.id + 0.2 }}
          >
            {item.title}
          </motion.p>
        </motion.div>
      ))}
    </motion.aside>
  );
};

export default MyEarningsSidenav;
