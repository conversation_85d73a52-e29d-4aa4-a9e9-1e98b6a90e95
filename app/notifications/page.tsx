import React from 'react';
import IndexClientsNotifications from './index-clients';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Notifications - IndianCashback',
  description:
    'Stay updated with the latest offers, cashback deals, and important account information from IndianCashback. Get real-time alerts about new deals, cashback status changes, and personalized recommendations.',
  alternates: {
    canonical: 'https://www.indiancashback.com/notifications',
  },
  openGraph: {
    url: 'https://www.indiancashback.com/notifications',
    title: 'Notifications - IndianCashback',
    description:
      'Stay updated with the latest offers, cashback deals, and important account information from IndianCashback. Get real-time alerts about new deals, cashback status changes, and personalized recommendations.',
  },
};

const Page = () => {
  return <IndexClientsNotifications />;
};

export default Page;
