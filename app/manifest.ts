import { MetadataRoute } from 'next';

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: 'IndianCashback',
    short_name: 'ICB',
    display_override: ['window-controls-overlay'],
    icons: [
      {
        src: '/icon-192x192.png',
        sizes: '192x192',
        type: 'image/png',
        purpose: 'any',
      },
      {
        src: '/icon-512x512.png',
        sizes: '512x512',
        type: 'image/png',
      },
    ],
    theme_color: '#574abe',
    background_color: '#574abe',
    start_url: '/',
    display: 'standalone',
    orientation: 'portrait',
    scope: '/',
    dir: 'ltr',
    lang: 'en-US',
    prefer_related_applications: false,
    categories: ['cashback', 'coupons', 'shopping'],
    screenshots: [
      {
        src: '/Overview.png',
        sizes: '410x915',
        type: 'image/png',
      },
      {
        src: '/Store.png',
        sizes: '410x915',
        type: 'image/png',
      },
      {
        src: '/Giftcard.png',
        sizes: '410x915',
        type: 'image/png',
      },
      {
        src: '/LeftNavigation.png',
        sizes: '410x915',
        type: 'image/png',
      },
      {
        src: '/MyEarnings.png',
        sizes: '410x915',
        type: 'image/png',
      },
    ],
    shortcuts: [
      {
        name: 'My Earnings',
        url: '/my-earnings-overview',
        icons: [
          {
            src: '/icons/network.png',
            sizes: '96x96',
          },
        ],
      },
      {
        name: 'View Dashboard',
        url: '/user',
        icons: [
          {
            src: '/icons/dashboard.png',
            sizes: '96x96',
          },
        ],
      },
    ],
    
  };
}
