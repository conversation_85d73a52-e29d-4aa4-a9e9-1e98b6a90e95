import type { CustomSearchParamsTypes } from '@/types/global-types';
import { BASE_URL } from '@/config';
import IndexStoreClients from './index-clients';
import type {
  CashbackRateType,
  DealAndCouponsResponse,
  GetAllReviewsResponse,
  GetCashbackRatesByStoreResponse,
  GetStoreDetailsResponse,
} from '@/services/api/data-contracts';
import { cookies } from 'next/headers';
import fetchWrapper from '@/utils/fetch-wrapper';
import type { Metadata } from 'next';
import { generateStoreSchema } from '@/utils/schema';

export async function generateMetadata({
  params,
}: {
  params: { slug: string };
}): Promise<Metadata> {
  try {
    const storeName =
      typeof params.slug === 'string'
        ? decodeURIComponent(params.slug)
        : params.slug;

    const resData = await getStoreData(
      {} as CustomSearchParamsTypes,
      storeName
    );
    const currentYear = new Date().getFullYear();

    return {
      title: `${resData?.store?.name} - Offers, Deals & coupon codes, ${currentYear}`,
      description: `'Visit ${resData?.store?.name} and get rewarded for every purchase you make - ${resData?.store?.name} coupons, promo codes and discount offers.`,
      alternates: {
        canonical: `https://www.indiancashback.com/store/${encodeURIComponent(
          storeName
        )}`,
      },
      openGraph: {
        url: `https://www.indiancashback.com/store/${resData?.store?.name}`,
      },
    };
  } catch (err) {
    console.error(err);
    return {
      title: 'Store Not Found',
      description: 'The requested store could not be found.',
    };
  }
}

async function getStoreData(
  searchParams: CustomSearchParamsTypes,
  storeName: string
) {
  const {
    searchParam = '',
    sortType = 'newest',
    subCategories = '',
    userType = 'both',
    offerType = 'deals',
    page = '1',
    pageSize = '15',
  } = searchParams;

  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  const res = await fetchWrapper<GetStoreDetailsResponse>(
    `${BASE_URL}/stores/store-details${storeName}?searchParam=${searchParam}&sortType=${sortType}&userType=${userType}&offerType=${offerType}&subCategories=${subCategories}&page=${page}&pageSize=${pageSize}`,
    {
      token: token?.value,
      suppressToast: true,
    }
  );
  return res;
}

async function getStoreOffers(
  searchParams: CustomSearchParamsTypes,
  storeId: string
) {
  const {
    searchParam = '',
    sortType = 'popular',
    subCategories = '',
    userType = 'both',
    page = '1',
    pageSize = '15',
  } = searchParams;

  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');

  const res = await fetchWrapper<DealAndCouponsResponse>(
    `${BASE_URL}/offers/deals-and-coupons?searchParam=${searchParam}&sortType=${sortType}&storeId=${storeId}&userType=${userType}&offerType=deals&subCategories=${subCategories}&page=${page}&pageSize=${pageSize}`,
    {
      token: token?.value,
    }
  );
  return res;
}

async function getStoreCoupons(
  searchParams: CustomSearchParamsTypes,
  storeId: string
) {
  const {
    searchParam = '',
    sortType = 'popular',
    subCategories = '',
    userType = 'both',
    page = '1',
    pageSize = '15',
  } = searchParams;

  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  // console.log(`${BASE_URL}/offers/deals-and-coupons?searchParam=${searchParam}&sortType=${sortType}&
  //       storeId=${storeId}&userType=${userType}&
  //       offerType=coupons&subCategories=${subCategories}&page=${page}&pageSize=${pageSize}`)
  const res = await fetchWrapper<DealAndCouponsResponse>(
    `${BASE_URL}/offers/deals-and-coupons?searchParam=${searchParam}&sortType=${sortType}&storeId=${storeId}&userType=${userType}&offerType=coupons&subCategories=${subCategories}&page=${page}&pageSize=${pageSize}`,
    {
      token: token?.value,
      cache: 'no-store',
    }
  );
  return res;
}

async function getStoreReviews(
  searchParams: CustomSearchParamsTypes,
  storeId: string
) {
  const { reviewSortType, page = '1', pageSize = '15' } = searchParams;
  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  let queryParams = `storeId=${storeId}&page=${page}&pageSize=${pageSize}`;

  if (reviewSortType) {
    queryParams += `&sortType=${reviewSortType}`;
  }

  const res = await fetchWrapper<GetAllReviewsResponse>(
    `${BASE_URL}/stores/review?${queryParams}`,
    {
      token: token?.value,
    }
  );
  return res;
}

async function getCbRatesData(storeId: string) {
  const res = await fetchWrapper<GetCashbackRatesByStoreResponse>(
    `${BASE_URL}/stores/cashback-rates-by-store${storeId}`
  );
  return res;
}

const Page = async ({
  params,
  searchParams,
}: {
  params: { slug: string };
  searchParams: CustomSearchParamsTypes;
}) => {
  // console.log(searchParams, 'search params');
  const storeName =
    typeof params.slug === 'string'
      ? decodeURIComponent(params.slug)
      : params.slug;

  let resData: GetStoreDetailsResponse;
  let storeOffers: DealAndCouponsResponse;
  let storeCoupons: DealAndCouponsResponse;
  let storeReviews: GetAllReviewsResponse;
  let cbRatesData: CashbackRateType[] = [];

  try {
    resData = await getStoreData(searchParams, storeName);
    storeOffers = await getStoreOffers(searchParams, resData.store.id);
    storeCoupons = await getStoreCoupons(searchParams, resData.store.id);
    storeReviews = await getStoreReviews(searchParams, resData.store.id);

    const resCbRates = await getCbRatesData(resData.store.id);
    cbRatesData = resCbRates.cashbackRates;
  } catch (err: any) {
    console.log({ err });
    return err;
  }

  // Generate Store Schema.org structured data
  const storeSchema = generateStoreSchema({
    name: resData.store.name,
    description:
      resData.store.description ||
      `Get the best cashback deals and offers from ${resData.store.name}`,
    image: resData.store.logo,
    url: `https://www.indiancashback.com/store/${encodeURIComponent(
      storeName
    )}`,
    offers:
      storeOffers?.offers?.slice(0, 3).map((offer) => ({
        '@type': 'Offer',
        name: offer.offerTitle,
        description:
          offer.offerCaption ||
          `${offer.offerTitle} - Get the best deals and cashbacks at ${resData.store.name}`,
        url: `https://www.indiancashback.com/offer/${encodeURIComponent(
          offer.offerTitle
        )}`,
      })) || [],
  });

  return (
    <>
      <script
        dangerouslySetInnerHTML={{ __html: storeSchema }}
        type='application/ld+json'
      />
      <IndexStoreClients
        cbRatesData={cbRatesData}
        data={resData}
        storeCoupons={storeCoupons}
        storeOffers={storeOffers}
        storeReviews={storeReviews}
      />
    </>
  );
};

export default Page;
export const dynamic = 'force-dynamic';
