import React from 'react';
import IndexClientsAllLinks from './index-clients';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Site Map - IndianCashback',
  description:
    'Navigate through our complete site map to easily find all sections of IndianCashback. Discover cashback offers, coupons, deals, store listings, and user resources to maximize your online shopping savings.',
  alternates: {
    canonical: 'https://www.indiancashback.com/all-links',
  },
  openGraph: {
    url: 'https://www.indiancashback.com/all-links',
    title: 'Site Map - IndianCashback',
    description:
      'Navigate through our complete site map to easily find all sections of IndianCashback. Discover cashback offers, coupons, deals, store listings, and user resources to maximize your online shopping savings.',
  },
};

const Page = () => {
  return <IndexClientsAllLinks />;
};

export default Page;
