import React from 'react';
import IndexClientsAboutUs from './index-clients';
import { Metadata } from 'next';
import { generateOrganizationSchema } from '@/utils/schema';

export const metadata: Metadata = {
  title: "About IndianCashback - India's Top Cashback Site",
  description:
    'Discover how IndianCashback helps you save money while shopping online. We partner with 600+ stores to offer the best cashback rates, exclusive deals, and discount coupons across all shopping categories.',
  alternates: {
    canonical: 'https://www.indiancashback.com/about-us',
  },
  openGraph: {
    url: 'https://www.indiancashback.com/about-us',
    title: "About IndianCashback - India's Top Cashback Site",
    description:
      'Discover how IndianCashback helps you save money while shopping online. We partner with 600+ stores to offer the best cashback rates, exclusive deals, and discount coupons across all shopping categories.',
  },
};

const Page = () => {
  // Generate detailed organization schema for About Us page
  const organizationSchema = generateOrganizationSchema({
    name: 'IndianCashback.com',
    url: 'https://www.indiancashback.com',
    logo: 'https://www.indiancashback.com/img/logo.png',
    description:
      "IndianCashback.com is one of India's leading cashback and coupons website that helps users save money while shopping online. We partner with 600+ stores across various categories including fashion, electronics, travel, and more to offer the best cashback rates and discount coupons.",
    sameAs: [
      'https://www.facebook.com/indiancashback',
      'https://twitter.com/indiancashback',
      'https://www.instagram.com/indiancashback',
      'https://www.linkedin.com/company/indiancashback',
      'https://www.youtube.com/channel/indiancashback',
    ],
  });

  return (
    <>
      <script
        dangerouslySetInnerHTML={{ __html: organizationSchema }}
        type='application/ld+json'
      />
      <IndexClientsAboutUs />
    </>
  );
};

export default Page;
