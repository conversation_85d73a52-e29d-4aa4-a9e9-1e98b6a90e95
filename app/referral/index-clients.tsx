import React from 'react';

// Section components (to be implemented in app/components/referral/)
import HowItWorksMain from '../components/referral-and-link-generator/HowItWorks';
import ReferFriendFAQ from '../components/referral-and-link-generator/ReferFriendFAQ';
import ReferFriendRewardsSummary from '../components/referral-and-link-generator/ReferFriendRewardsSummary';
import ReferFriendHeroSection from '../components/referral-and-link-generator/ReferFriendHeroSection';
import ReferFriendWithdrawalInfo from '../components/referral-and-link-generator/ReferFriendWithdrawalInfo';
import CommonHeader from '../components/headers/common-header';
import { GetReferralLeaderboardResponse } from '@/services/api/data-contracts';
import ReferFriendTopReferrers from '../components/referral-and-link-generator/ReferFriendTopReferrers';

type props = {
  referralData: GetReferralLeaderboardResponse[];
};

const IndexClientsReferEarn = ({ referralData }: props) => {
  return (
    <>
      <CommonHeader headline='Refer & Earn' />
      <div className='w-full min-h-screen bg-background p-3 flex flex-col justify-start gap-y-4'>
        <ReferFriendHeroSection />
        <HowItWorksMain type='referral' />
        <ReferFriendRewardsSummary />
        <ReferFriendTopReferrers data={referralData} />
        <ReferFriendWithdrawalInfo />
        <ReferFriendFAQ type='referral' />
      </div>
    </>
  );
};

export default IndexClientsReferEarn;
