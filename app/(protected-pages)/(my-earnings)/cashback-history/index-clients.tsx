'use client';
import C<PERSON><PERSON><PERSON><PERSON>Accordian from '@/app/components/accordians/cashback-history-accordian';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import CommonHeader from '@/app/components/headers/common-header';
import MyEarningsSidenav from '@/app/components/my-earnings/my-earnings-sidenav';
import MyEarningsToolbar from '@/app/components/my-earnings/my-earnings-toolbar';
import NoData from '@/app/components/no-data';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import {
  setHideSearchFilter,
  setHideSortFilter,
  setShowStoresFilter,
  setSingleDatePicker,
  setSortItems,
  setStatusList,
  setTitle,
  setTotalFiltersApplied,
} from '@/redux/slices/earnings-toolbar-slice';
import { setSearchValue } from '@/redux/slices/global-search-slice';
import {
  GetCbHistoryResponse,
  StatusEnum1,
} from '@/services/api/data-contracts';
import {
  useCreateMultiQueryString,
  useCreateQueryString,
} from '@/utils/custom-hooks';
import { ConfigProvider, MenuProps, Pagination, theme } from 'antd';
import { useTheme } from 'next-themes';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const sortItems = [
  {
    label: 'Newest',
    key: 'newest',
  },
  {
    label: 'Oldest',
    key: 'oldest',
  },
  {
    label: 'Cashback Amount',
    key: 'cashbackAmount',
  },
];

const statusList: StatusEnum1[] = [
  StatusEnum1.Pending,
  StatusEnum1.Confirmed,
  StatusEnum1.Cancelled,
];

const IndexClientsCBHistory = ({ data }: { data: GetCbHistoryResponse }) => {
  // const [searchvalue, setSearchValue] = useState('');
  const [activeId, setActiveId] = useState(1);
  const dispatch = useAppDispatch();
  const { searchValue } = useAppSelector((state) => state.commonFilters);
  const { resolvedTheme } = useTheme();
  const { replace } = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const createMultiQueryString = useCreateMultiQueryString(searchParams);
  const createQueryString = useCreateQueryString(searchParams);
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);

  useEffect(() => {
    dispatch(setTitle('My Earnings'));
    const searchParam = searchParams.get('searchParam');
    if (searchParam) {
      dispatch(setSearchValue(searchParam));
    }
    setSelectedStatus(searchParams.get('status')?.split(',') || []);
  }, [dispatch, searchParams]);

  console.log(pathname, 'path name');

  useEffect(() => {
    dispatch(setSortItems(sortItems));
    dispatch(setStatusList(statusList));
    dispatch(setShowStoresFilter(false));
    dispatch(setHideSearchFilter(false));
    dispatch(setHideSortFilter(false));
    dispatch(setSingleDatePicker(false));
  }, [dispatch]);

  const onClickSortBy: MenuProps['onClick'] = ({ key }) => {
    replace(pathname + '?' + createQueryString('sortType', key));
  };

  useEffect(() => {
    let filterCount = 0;
    if (selectedStatus.length > 0) {
      filterCount += selectedStatus.length;
    }
    dispatch(setTotalFiltersApplied(filterCount));
  }, [dispatch, selectedStatus]);

  const onApply = async (options?: { selectedStatus?: string[] }) => {
    const { selectedStatus = [] } = options ?? {};
    const hasSelectedStatus = selectedStatus.length > 0;

    if (!hasSelectedStatus) {
      // Clear existing filters
      setSelectedStatus([]);
      replace(
        pathname + '?' + createMultiQueryString([{ name: 'status', value: '' }])
      );
      return;
    }

    const selectedStatusString = selectedStatus.join(',');
    setSelectedStatus(selectedStatus);

    const queries = [{ name: 'status', value: selectedStatusString }];
    const queryString = createMultiQueryString(queries);
    replace(pathname + '?' + queryString);
  };

  const onClear = () => {
    setSelectedStatus([]);
    const queries = [{ name: 'status', value: '' }];
    const queryString = createMultiQueryString(queries);
    replace(pathname + '?' + queryString);
  };

  const handleToggle = (id: number) => {
    if (activeId === id) {
      setActiveId(0);
    } else {
      setActiveId(id);
    }
  };

  return (
    <>
      <CommonHeader headline='My Earnings' subHeading={<span>Overview</span>} />

      <motion.section
        animate={{ opacity: 1 }}
        className='max-w-[1280px] min-[1280px]:mx-auto'
        initial={{ opacity: 0 }}
        onClick={(e) => {
          e.stopPropagation();
        }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'User Page', link: '/user' },
              { title: 'My Earnings', link: '/' },
            ]}
            rootClass='sticky top-[104px] z-[9]'
          />
        </motion.div>
        <motion.div
          animate={{ opacity: 1 }}
          className='w-full flex z-[0] relative'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <motion.div
            animate={{ opacity: 1, x: 0 }}
            initial={{ opacity: 0, x: -30 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <MyEarningsSidenav activeNavId={2} />
          </motion.div>
          <motion.div
            animate={{ opacity: 1, x: 0 }}
            className='bg-[#E0E0E0] dark:bg-[#1F222A] w-full ml-[2px] pb-[80px]'
            initial={{ opacity: 0, x: 30 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              initial={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <MyEarningsToolbar
                onApply={onApply}
                onClear={onClear}
                onClickSortBy={onClickSortBy}
                searchKey={searchValue}
                selectedStatusArray={selectedStatus}
              />
            </motion.div>
            <motion.div
              animate={{ opacity: 1 }}
              className='pt-[10px] px-[6px] lg:px-[15px] xl:px-[30px] flex flex-col gap-y-[10px]'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <AnimatePresence>
                {data.cbItems.length ? (
                  data.cbItems.map((item, index) => (
                    <motion.div
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      initial={{ opacity: 0, y: 20 }}
                      key={index}
                      transition={{ duration: 0.3, delay: 0.7 + index * 0.05 }}
                    >
                      <CbHistoryAccordian
                        activeId={activeId}
                        data={{
                          id: index + 1,
                          orderDate: item.orderDate,
                          storeImgUrl: item.storeLogo,
                          cashbackAmount: item.cashbackAmount,
                          orderAmount: item.orderAmount,
                          remarks: item.remarks,
                          RefId: item.referenceId,
                          status: item.status,
                        }}
                        key={index}
                        onClick={handleToggle}
                      />
                    </motion.div>
                  ))
                ) : (
                  <motion.div
                    animate={{ opacity: 1 }}
                    initial={{ opacity: 0 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    <NoData imgClass='!w-[200px] !h-[200px] lg:!w-[300px] lg:!h-[300px]' />
                  </motion.div>
                )}
              </AnimatePresence>

              {data && data?.pagination?.pageSize > 0 && (
                <motion.div
                  animate={{ opacity: 1, y: 0 }}
                  className='flex-center w-full my-[50px]'
                  initial={{ opacity: 0, y: 20 }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                >
                  <ConfigProvider
                    theme={{
                      algorithm:
                        resolvedTheme === 'dark'
                          ? theme.darkAlgorithm
                          : theme.defaultAlgorithm,
                    }}
                  >
                    <Pagination
                      defaultCurrent={1}
                      defaultPageSize={15}
                      onChange={(pageNumber, pageSize) =>
                        replace(
                          pathname +
                            '?' +
                            createMultiQueryString([
                              { name: 'page', value: pageNumber.toString() },
                              { name: 'pageSize', value: pageSize.toString() },
                            ])
                        )
                      }
                      responsive
                      total={data.pagination.total}
                    />
                  </ConfigProvider>
                </motion.div>
              )}
            </motion.div>
          </motion.div>
        </motion.div>
      </motion.section>
    </>
  );
};

export default IndexClientsCBHistory;
