import React from 'react';
import IndexClientsCBHistory from './index-clients';
import {
  GetCbHistoryResponse,
  UserControllerGetCashbackHistoryParams,
} from '../../../../services/api/data-contracts';
import { cookies } from 'next/headers';
import fetchWrapper from '@/utils/fetch-wrapper';
import { BASE_URL } from '@/config';

interface customSearchParams extends UserControllerGetCashbackHistoryParams {
  page: number;
  pageSize: number;
}
const getCashbackHistory = async (searchParams: customSearchParams) => {
  const {
    searchParam,
    sortType = 'newest',
    status,
    stores,
    page,
    pageSize,
    startDate,
    endDate,
  } = searchParams;

  const queryParams = Object.entries({
    searchParam,
    sortType,
    status,
    stores,
    page,
    pageSize,
    startDate,
    endDate,
  })
    .filter(([_, value]) => value) //eslint-disable-line
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return fetchWrapper<GetCbHistoryResponse>(
    `${BASE_URL}/users/cashback-history?${queryParams}`,
    {
      token: token?.value,
    }
  );
};

const Page = async ({ searchParams }: { searchParams: any }) => {
  let data: GetCbHistoryResponse;
  try {
    data = await getCashbackHistory(searchParams);
  } catch (error) {
    return error;
  }
  return <IndexClientsCBHistory data={data} />;
};

export default Page;
