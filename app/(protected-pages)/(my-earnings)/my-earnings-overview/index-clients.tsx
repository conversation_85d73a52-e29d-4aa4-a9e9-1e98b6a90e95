'use client';
import React from 'react';
import { motion } from 'framer-motion';
import CommonHeader from '@/app/components/headers/common-header';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import MyEarningsSidenav from '@/app/components/my-earnings/my-earnings-sidenav';
import InfoHighligtedCard from '@/app/components/user-page-components/info-highligted-card';
import PendingSVG from '@/app/components/svg/pending';
import CancelledSVG from '@/app/components/svg/cancelled';
import ApprovedSVG from '@/app/components/svg/approved';
import LinkContainerWithoutCaption, {
  LinkContainerMyEarnings,
} from '@/app/components/my-earnings/link-container';
import Support24SVG from '@/app/components/svg/support24';
import FaqSVG from '@/app/components/svg/faq';
import TotalCbEarned from '@/app/components/svg/total-cb-earned';
import ReferFriendSVG from '@/app/components/svg/refer-freind';
import WalletAddSVG from '@/app/components/svg/wallet-add';
import Image from 'next/image';
import MyEarningsChart from '@/app/components/my-earnings/my-earnings-chart';
import { UserOverviewResponse } from '@/services/api/data-contracts';
import { formatIndRs } from '@/utils/helpers';
import { useRouter } from 'next/navigation';

const IndexClientsMyEarnings = ({ data }: { data: UserOverviewResponse }) => {
  const router = useRouter();
  return (
    <>
      <CommonHeader headline='My Earnings' subHeading={<span>Overview</span>} />

      <motion.section
        animate={{ opacity: 1 }}
        className='max-w-[1280px] min-[1280px]:mx-auto'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'User Page', link: '/user' },
              { title: 'My Earnings', link: '/' },
            ]}
            rootClass='sticky top-[104px] z-[9]'
          />
        </motion.div>
        <motion.div
          animate={{ opacity: 1 }}
          className='w-full flex'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <MyEarningsSidenav activeNavId={1} />
          <motion.div
            animate={{ opacity: 1, x: 0 }}
            className='bg-[#E0E0E0] dark:bg-[#1F222A] w-full ml-[2px] pb-[40px] px-[6px]'
            initial={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='bg-container pt-[14px] px-[10px] pb-[30px] lg:pt-[24px] border-[0.5px] border-white dark:border-[#353943] rounded-[10px] max-w-[950px] mx-auto mt-[23px]'
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              whileHover={{ boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
            >
              <motion.div
                animate={{ opacity: 1 }}
                className='flex items-center justify-between px-[7px] lg:px-0 text-blackWhite'
                initial={{ opacity: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <motion.h4
                  animate={{ opacity: 1, x: 0 }}
                  className='text-xs lg:text-sm font-pat'
                  initial={{ opacity: 0, x: -10 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  My Earnings
                </motion.h4>
              </motion.div>

              <motion.div
                animate={{ opacity: 1, y: 0 }}
                className='flex justify-center gap-x-[6px] lg:gap-x-[14px] mt-[13px] lg:mt-[6px]'
                initial={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: 0.7 }}
              >
                <InfoHighligtedCard
                  caption='Pending'
                  icon={<PendingSVG className='text-blackWhite w-[15px]' />}
                  number={data.totalPendingCount}
                  onClick={() =>
                    router.push('/cashback-history?status=pending')
                  }
                  stripColorClass='bg-[#FFC554]'
                />
                <InfoHighligtedCard
                  caption='Approved'
                  icon={<CancelledSVG className='text-blackWhite w-[15px]' />}
                  number={data.totalApprovedCount}
                  onClick={() =>
                    router.push('/cashback-history?status=approved')
                  }
                  stripColorClass='bg-[#69C8B4]'
                />
                <InfoHighligtedCard
                  caption='Cancelled'
                  icon={<ApprovedSVG className='text-blackWhite w-[15px]' />}
                  number={data.totalCancelledCount}
                  onClick={() =>
                    router.push('/cashback-history?status=cancelled')
                  }
                  stripColorClass='bg-[#F06B6B]'
                />
              </motion.div>
              <motion.div
                animate={{ opacity: 1 }}
                className='flex flex-col items-center md:grid md:grid-cols-2 justify-items-center mt-[20px] gap-y-[7px] md:gap-[15px]'
                initial={{ opacity: 0 }}
                transition={{ duration: 0.5, delay: 0.8 }}
              >
                <LinkContainerMyEarnings
                  amount={formatIndRs(data.totalCashbackEarned)}
                  icon={
                    <TotalCbEarned className='w-[12px] lg:w-[18px] text-[#818181] dark:text-white' />
                  }
                  onClick={() => router.push('/cashback-history')}
                  title='Total Cashback Earned'
                />
                <LinkContainerMyEarnings
                  amount={formatIndRs(data.totalReferralCommission)}
                  icon={
                    <ReferFriendSVG className='w-[14px] lg:w-[19px] text-[#818181] dark:text-white' />
                  }
                  onClick={() => router.push('/referral-history')}
                  title='Total Referral Commission Earned'
                />
                <LinkContainerMyEarnings
                  amount={formatIndRs(data.readyToWithdraw)}
                  icon={
                    <WalletAddSVG className='w-[12px] lg:w-[25px] text-[#818181] dark:text-white' />
                  }
                  onClick={() =>
                    router.push('/cashback-history?status=confirmed')
                  }
                  title='Ready to Withdraw'
                />
                <LinkContainerMyEarnings
                  amount={formatIndRs(data.flipkartRewardPoints)}
                  icon={
                    <Image
                      alt='reward points'
                      className='w-[32px] lg:w-[50px]'
                      height={32}
                      src='/img/flipkart-icon.png'
                      title='reward points'
                      width={32}
                    />
                  }
                  title='Flipkart Reward Points'
                />
              </motion.div>
            </motion.div>
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.9 }}
              whileHover={{ boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
            >
              <MyEarningsChart
                monthlyCashback={data.totalCashback}
                monthlyClicks={data.totalClicks}
                monthlyOrderAmount={data.totalOrderAmount}
              />
            </motion.div>
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='flex flex-col md:flex-row md:justify-center gap-y-[13px] md:gap-x-[20px] mt-[30px]'
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 1 }}
            >
              <LinkContainerWithoutCaption
                icon={
                  <FaqSVG className='w-[15px] lg:w-[24px] text-[#818181] dark:text-white' />
                }
                title='Related Question and Answers'
              />
              <LinkContainerWithoutCaption
                icon={
                  <Support24SVG className='w-[12px] lg:w-[20px] text-[#818181] dark:text-white' />
                }
                title='Support'
              />
            </motion.div>
          </motion.div>
        </motion.div>
      </motion.section>
    </>
  );
};

export default IndexClientsMyEarnings;
