import React from 'react';
import IndexClientsPayments from './index-clients';
import { cookies } from 'next/headers';
import fetchWrapper from '@/utils/fetch-wrapper';
import { GetBankAccountDataResponse } from '@/services/api/data-contracts';
import { BASE_URL } from '@/config';
const getBankDetails = async () => {
  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return fetchWrapper<GetBankAccountDataResponse>(
    `${BASE_URL}/users/get-bank-details`,
    {
      token: token?.value,
    }
  );
};
const Page = async () => {
  let data: GetBankAccountDataResponse;
  try {
    data = await getBankDetails();
  } catch (error) {
    return error;
  }
  return <IndexClientsPayments data={data} />;
};

export default Page;
