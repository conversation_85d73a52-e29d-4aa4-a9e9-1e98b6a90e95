import PillButton from '@/app/components/atoms/pills';
import ThemeButton from '@/app/components/atoms/theme-btn';
import { LoadingGif } from '@/app/components/misc/loading-components';
import WalletAddSVG from '@/app/components/svg/wallet-add';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { setRefetchUserDetails } from '@/redux/slices/auth-slice';
import {
  GetBankAccountDataResponse,
  PaymentTypes,
} from '@/services/api/data-contracts';
import fetchWrapper from '@/utils/fetch-wrapper';
import { formatIndRs } from '@/utils/helpers';
import { Radio, Slider } from 'antd';
import React, { useRef, useState } from 'react';
import { toast } from 'react-toastify';
import { motion } from 'framer-motion';
import SmartLink from '@/app/components/common/smart-link';

type QuickAmountType = '0' | '500' | '1000' | '5000' | '10000';
const GoToBankDetailsPage = ({ text }: { text: string }) => {
  return (
    <motion.div
      transition={{ duration: 0.2 }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <SmartLink
        className='hover:border-[1px] bg-[#4D3EC1] !text-white px-[10px] h-[30px] rounded-[5px] flex-center text-xs font-medium'
        href={'/bank-details'}
      >
        {text}
      </SmartLink>
    </motion.div>
  );
};

const BankUpiRedeemForm = ({
  data,
  setShowWithdrawModal,
}: {
  data: GetBankAccountDataResponse;
  setShowWithdrawModal: (value: boolean) => void;
}) => {
  const { userDetails, refetchUserDetails } = useAppSelector(
    (state) => state.auth
  );
  const [paymentType, setPaymentType] = useState<PaymentTypes>(
    PaymentTypes.Bank
  );
  const withdrawAmountRef = useRef<number | string>('');
  const [, forceUpdate] = React.useReducer((x) => x + 1, 0);
  const [quickAmount, setQuickAmount] = useState<QuickAmountType>('0');
  const [errMsg, setErrMsg] = useState('');
  const [loadingWithdraw, setLoadingWithdraw] = useState(false);
  const dispatch = useAppDispatch();

  const withDrawAmounthandler = async () => {
    if (!Number(withdrawAmountRef?.current)) {
      setErrMsg('Please enter a amount to withdraw above ₹200');
      return;
    }
    if (/[^0-9]/g.test(withdrawAmountRef?.current.toString())) {
      setErrMsg('Please enter valid number digits only');
      return;
    }
    if (Number(withdrawAmountRef?.current) < 200) {
      return setErrMsg('Withdraw amount must be above ₹200');
    }
    setErrMsg('');
    try {
      setLoadingWithdraw(true);
      await fetchWrapper('/api/proxy/payment/withdraw', {
        method: 'POST',
        body: JSON.stringify({
          withdrawAmount: Number(withdrawAmountRef.current),
          paymentType,
        }),
      });
      toast.success(
        `Your request to withdraw ${formatIndRs(
          Number(withdrawAmountRef.current) || 0
        )}  ${
          paymentType === PaymentTypes.Bank
            ? 'to your Bank AC No' +
              data.accountNumber
                .toString()
                .slice(-4)
                .padStart(data.accountNumber.toString().length, 'X')
            : 'to your UPI Id' + data.upi
        } recorded successfully.  It will be processed within 2-3 business days.`
      );
      dispatch(setRefetchUserDetails(!refetchUserDetails));
      setLoadingWithdraw(false);
      setShowWithdrawModal(false);
    } catch (error) {
      console.error('Error withdrawing amount:', error);
      setLoadingWithdraw(false);
    }
  };

  const inputAmountHandler = (event: React.ChangeEvent<HTMLInputElement>) => {
    withdrawAmountRef.current = event.target.value.replace(/[^0-9]/g, '');
    // Trigger a re-render
    forceUpdate();
  };
  const sliderAmountHandler = (amount: number) => {
    withdrawAmountRef.current = Number(
      amount.toString().replace(/[^0-9]/g, '')
    );
    // Trigger a re-render
    forceUpdate();
  };

  return (
    <motion.div
      animate={{ opacity: 1 }}
      className='text-blackWhite'
      initial={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className='mx-[26px] flex flex-col bg-[#FFC554] rounded-[5px] shadow-sm mt-[10px] py-[10px] px-[20px]'
        initial={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        whileHover={{ boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
      >
        <motion.div
          animate={{ opacity: 1 }}
          className='flex items-center gap-x-[10px] mx-auto'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <WalletAddSVG className='text-black w-[11px] lg:w-[15px]' />
          <span className='text-[8px] lg:text-xs font-medium text-black'>
            Ready to Withdraw
          </span>
        </motion.div>
        <motion.h4
          animate={{ opacity: 1, scale: 1 }}
          className='text-center text-[11px] lg:text-lg font-nexa font-black text-black mt-[7px]'
          initial={{ opacity: 0, scale: 0.8 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          {formatIndRs(userDetails?.balance || 0)}
        </motion.h4>
      </motion.div>

      <motion.h4
        animate={{ opacity: 1, x: 0 }}
        className='text-xs ml-[26px] font-pat mt-[20px] capitalize'
        initial={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        Select Bank/UPI
      </motion.h4>
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className='mt-[5px] w-full bg-[#E3E3E3] dark:bg-[#33363F] px-[26px] py-[17px]'
        initial={{ opacity: 0, y: 20 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        whileHover={{ boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
      >
        <motion.div
          animate={{ opacity: 1 }}
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <Radio.Group
            defaultValue={PaymentTypes.Bank}
            name='redeemByBankOrUPI'
          >
            <Radio
              checked={paymentType === PaymentTypes.Bank}
              className='text-[10px] lg:text-xs font-medium text-blackWhite'
              defaultChecked={true}
              onClick={() => setPaymentType(PaymentTypes.Bank)}
              value={PaymentTypes.Bank}
            >
              Bank
            </Radio>
            {/* <Radio
            className='text-[10px] lg:text-xs font-medium text-blackWhite'
            onClick={() => setPaymentType('upi')}
            value={'upi'}
          >
            UPI Id
          </Radio> */}
          </Radio.Group>
        </motion.div>
        <motion.div
          animate={{ opacity: 1 }}
          className='mt-[15px] flex items-center justify-between'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
        >
          <>
            {paymentType === PaymentTypes.Bank ? (
              data.accountNumber ? (
                <>
                  <motion.div
                    animate={{ opacity: 1, x: 0 }}
                    className='text-[8px] lg:text-xs font-medium'
                    initial={{ opacity: 0, x: -10 }}
                    transition={{ duration: 0.3, delay: 0.8 }}
                  >
                    <span className='w-[77px] lg:w-[110px] text-[#858585] inline-block'>
                      Account Holder:
                    </span>
                    <span className='text-blackWhite'>{data.holderName}</span>
                    <br />
                    <span className='w-[77px] lg:w-[110px] text-[#858585] mt-[7px] inline-block'>
                      Account Number:
                    </span>
                    <span className='text-blackWhite'>
                      {data.accountNumber}
                    </span>
                    <br />
                  </motion.div>
                  <GoToBankDetailsPage text='Edit' />
                </>
              ) : (
                <GoToBankDetailsPage text='Add Bank Details' />
              )
            ) : data.upi ? (
              <>
                <motion.div
                  animate={{ opacity: 1, x: 0 }}
                  className='text-[8px] lg:text-xs font-medium'
                  initial={{ opacity: 0, x: -10 }}
                  transition={{ duration: 0.3, delay: 0.8 }}
                >
                  <span className='w-[77px] lg:w-[110px] text-[#858585] inline-block'>
                    Account Holder:
                  </span>
                  <span className='text-blackWhite'>{data.upi}</span>
                  <br />
                </motion.div>
                <GoToBankDetailsPage text='Edit' />
              </>
            ) : (
              <GoToBankDetailsPage text='Add UPI Id' />
            )}
          </>
        </motion.div>
      </motion.div>
      {((paymentType === PaymentTypes.Bank && data.accountNumber) ||
        (paymentType === PaymentTypes.Upi && data.upi)) && (
        <motion.div
          animate={{ opacity: 1 }}
          className='px-[26px]'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.8 }}
        >
          <motion.h4
            animate={{ opacity: 1, x: 0 }}
            className='mt-[20px] text-xs font-pat capitalize'
            initial={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.5, delay: 0.9 }}
          >
            Amount to withdraw
          </motion.h4>
          <motion.div
            animate={{ opacity: 1 }}
            initial={{ opacity: 0 }}
            transition={{ duration: 0.5, delay: 1.0 }}
          >
            <Slider
              className='mt-[45px]'
              max={userDetails.balance}
              min={200}
              onChange={sliderAmountHandler}
              value={Number(withdrawAmountRef.current)}
            />
          </motion.div>
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className='flex-center mt-[20px]'
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 1.1 }}
          >
            <motion.label
              animate={{ opacity: 1 }}
              className='text-[8px] lg:text-xs text-[#858585] dark:text-white'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.3, delay: 1.2 }}
            >
              Enter Amount
            </motion.label>
            <motion.div
              animate={{ opacity: 1, x: 0 }}
              className='bg-[#CFCFCF] dark:bg-body flex-center w-[105px] lg:w-[110px] h-[40px] rounded-[5px] px-[10px] ml-[10px]'
              initial={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3, delay: 1.3 }}
              whileHover={{ boxShadow: '0px 4px 15px rgba(0, 0, 0, 0.1)' }}
            >
              <span className='shrink-0 text-blackWhite text-[18px] font-black'>
                ₹
              </span>
              <input
                className='ml-[4px] lg:ml-[10px] grow placeholder:text-[#A3A3A3] text-blackWhite bg-transparent outline-none w-full text-[18px] font-black '
                onChange={inputAmountHandler}
                placeholder='00,000'
                type='text'
                value={formatIndRs(Number(withdrawAmountRef.current)).slice(1)}
              />
            </motion.div>
          </motion.div>
          <motion.div
            animate={{ opacity: 1 }}
            className='flex gap-x-[8px] mt-[25px]'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.5, delay: 1.4 }}
          >
            {(['500', '1000', '5000', '10000'] as QuickAmountType[])
              .filter((item) => userDetails.balance >= Number(item))
              .map((item, index) => (
                <motion.div
                  animate={{ opacity: 1, y: 0 }}
                  initial={{ opacity: 0, y: 20 }}
                  key={item}
                  transition={{ duration: 0.3, delay: 1.5 + index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <PillButton
                    className='border-[1px] border-primary !font-bold'
                    isSelected={quickAmount === item}
                    key={item}
                    onClick={() => {
                      setQuickAmount(item);
                      withdrawAmountRef.current = Number(item);
                    }}
                    text={formatIndRs(Number(item))}
                  />
                </motion.div>
              ))}
          </motion.div>
          {!loadingWithdraw ? (
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 1.9 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <ThemeButton
                className='mt-[30px] !w-[114px] mx-auto hover:bg-primaryDark active:bg-primary'
                onClick={withDrawAmounthandler}
                text='REDEEM NOW'
              />
            </motion.div>
          ) : (
            <motion.div
              animate={{ opacity: 1 }}
              initial={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <LoadingGif className='!w-[40px] !h-[30px] mt-[20px]' />
            </motion.div>
          )}
          <motion.h6
            animate={{ opacity: errMsg ? 1 : 0 }}
            className='text-[red] text-[10px] lg:text-xs text-center mt-[10px]'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {errMsg}
          </motion.h6>
        </motion.div>
      )}
    </motion.div>
  );
};

export default BankUpiRedeemForm;
