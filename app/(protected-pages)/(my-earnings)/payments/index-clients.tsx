'use client';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import CommonHeader from '@/app/components/headers/common-header';
import MyEarningsSidenav from '@/app/components/my-earnings/my-earnings-sidenav';
import React, { useState } from 'react';
import PaymentRedeemCard from '@/app/components/my-earnings/payment-redeem-card';
import BottomDrawer from '@/app/components/atoms/BottomDrawer';
import BankUpiRedeemForm from './bank-upi-redeem-form';
import LinkContainerWithoutCaption from '@/app/components/my-earnings/link-container';
import PaymentSVG from '@/app/components/svg/payment-icon';
import FaqSVG from '@/app/components/svg/faq';
import Support24SVG from '@/app/components/svg/support24';
import CashbackHandSVG from '@/app/components/svg/cashback-hand';
import BankSVG from '@/app/components/svg/bank-icon';
import { Modal } from 'antd';
import CrossSVG from '@/app/components/svg/cross';
import { useRouter } from 'next/navigation';
import { GetBankAccountDataResponse } from '@/services/api/data-contracts';
import { useWindowSize } from 'usehooks-ts';
import { motion } from 'framer-motion';

const IndexClientsPayments = ({
  data,
}: {
  data: GetBankAccountDataResponse;
}) => {
  const router = useRouter();

  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  // const [toggleButton, setToggleButton] = useState(true);
  const { width = 0 } = useWindowSize();

  return (
    <>
      <CommonHeader headline='My Earnings' subHeading={<span>Overview</span>} />

      <motion.section
        animate={{ opacity: 1 }}
        className='max-w-[1280px] min-[1280px]:mx-auto'
        initial={{ opacity: 0 }}
        onClick={(e) => {
          e.stopPropagation();
        }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'User Page', link: '/user' },
              { title: 'My Earnings', link: '/' },
            ]}
            rootClass='sticky top-[104px] z-[9]'
          />
        </motion.div>
        <div className='w-full flex'>
          <MyEarningsSidenav activeNavId={3} />
          <div className='bg-[#E0E0E0] dark:bg-[#1F222A] w-full ml-[2px] pb-[80px] px-[6px]'>
            {/* <SlidingButton
              buttonDetails={[
                { title: 'Cashback', value: 'Cashback' },
                { title: 'Rewards', value: 'Rewards' },
              ]}
              defaultSelectedBtn={1}
              onChange={() => setToggleButton((prevState) => !prevState)}
              rootClassName='!mt-[15px] lg:mt-[21px]'
              uniqueId='payments'
            /> */}
            <div className='mt-[15px] lg:mt-[27px] bg-container rounded-[10px] shadow-sm pt-[14px] px-[8px] pb-[30px] lg:pt-[23px] lg:px-[20px] lg:mx-[20px] xl:mx-[50px]  text-blackWhite'>
              <h4 className='text-[11px] font-pat ml-[6px]'>
                Select Where To Redeem ?
              </h4>
              <div className='mt-[22px] grid grid-cols-2 gap-x-[8px] xl:gap-x-[20px] gap-y-[12px]'>
                <PaymentRedeemCard
                  caption='Coming soon!!'
                  // 'Load to your ICB Card'
                  imgUrl='/img/my-earnings/paymentBank.png'
                  offer={{ percent: 10 }}
                  title='ICB Card'
                />
                {/* <PaymentRedeemCard
                  caption='Use your ICB balance to pay your Bill Payments'
                  imgClass='lg:!w-[100px]'
                  imgUrl='/img/my-earnings/paymentBill.png'
                  offer={{ percent: 10, upto: true }}
                  title='Bill Payments'
                /> */}
                {/* <PaymentRedeemCard
                    caption='Buy ICB Giftcard'
                    imgClass='!w-[90px] lg:!w-[126px]'
                    imgUrl='/img/my-earnings/paymentGiftcard.png'
                    offer={{ percent: 10 }}
                    title='ICB GiftCArd'
                  /> */}
                <PaymentRedeemCard
                  caption='Transfer to Bank'
                  // / UPI'
                  imgUrl='/img/my-earnings/paymentIcbCard.png'
                  onClick={() => setShowWithdrawModal(true)}
                  title='Bank Transfer'
                  // /UPI'
                />
              </div>
              <div />
            </div>

            {/* --------------------Links--------------------- */}
            <div className='flex flex-col gap-[13px] md:grid md:grid-cols-2 lg:justify-items-center mt-[20px] lg:px-[20px] xl:px-[50px]'>
              <LinkContainerWithoutCaption
                icon={
                  <BankSVG className='w-[12px] lg:w-[18px] text-[#818181]' />
                }
                onClick={() => {
                  router.push('/bank-details');
                }}
                title='Update Bank/UPI'
              />
              <LinkContainerWithoutCaption
                icon={
                  <PaymentSVG className='w-[13px] lg:w-[21px] text-[#818181]' />
                }
                onClick={() => {
                  router.push(`/payment-history`);
                }}
                title='Payment History'
              />
              <LinkContainerWithoutCaption
                icon={
                  <FaqSVG className='w-[14px] lg:w-[24px] text-[#818181]' />
                }
                title='Related Question and Answers'
              />
              <LinkContainerWithoutCaption
                icon={
                  <Support24SVG className='w-[11px] lg:w-[20px] text-[#818181]' />
                }
                onClick={() => {
                  router.push('https://indiancashback.zendesk.com/');
                }}
                title='Support'
              />
              <LinkContainerWithoutCaption
                icon={
                  <CashbackHandSVG className='w-[13px] lg:w-[21px] text-[#818181]' />
                }
                onClick={() => {
                  router.push('/');
                }}
                title='Cashback Home'
              />
            </div>
          </div>
        </div>
      </motion.section>
      {width <= 1023 ? (
        <BottomDrawer
          maskClosable={false}
          onClose={() => setShowWithdrawModal(false)}
          open={showWithdrawModal}
          sectionClass='!px-0'
          title=''
          titleIcon={''}
        >
          <BankUpiRedeemForm
            data={data}
            setShowWithdrawModal={setShowWithdrawModal}
          />
        </BottomDrawer>
      ) : (
        <Modal
          cancelText=''
          centered
          classNames={{
            content: '!bg-container',
            header: '!bg-container !text-blackWhite',
          }}
          closeIcon={
            <CrossSVG className='text-blackWhite opacity-50 w-[12px]' />
          }
          destroyOnClose={true}
          footer={<></>}
          maskClosable={false}
          okText=''
          onCancel={() => setShowWithdrawModal(false)}
          open={showWithdrawModal}
          title={
            <h4 className='text-sm font-pat text-blackWhite font-normal'>
              Select an Amount to Redeem
            </h4>
          }
        >
          <BankUpiRedeemForm
            data={data}
            setShowWithdrawModal={setShowWithdrawModal}
          />
        </Modal>
      )}
    </>
  );
};

export default IndexClientsPayments;
