'use client';
import AboutHighlightCard from '@/app/components/atoms/about-highlight-card';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import ThemeButton from '@/app/components/atoms/theme-btn';
import CommonHeader from '@/app/components/headers/common-header';
import MyEarningsSidenav from '@/app/components/my-earnings/my-earnings-sidenav';
import Image from 'next/image';
import long_arrow from '@/public/svg/long-arrow.svg';
import {
  DottedLine3,
  DottedLine4,
} from '@/app/components/svg/patterns/dotted-lines';
import BottomDrawer from '@/app/components/atoms/BottomDrawer';
import { useEffect, useState } from 'react';
import { ShareSVG } from '@/app/components/svg/save';
import CopySVG from '@/app/components/svg/copy';
import { Modal } from 'antd';
import { useRouter } from 'next/navigation';
import { useAppSelector } from '@/redux/hooks';
import { APP_URL } from '@/config';
import { copyToClipboard } from '@/utils/helpers';
import { toast } from 'react-toastify';
import { RWebShare } from 'react-web-share';
import { motion, AnimatePresence } from 'framer-motion';
import ReferFriendBanner from '@/app/components/my-earnings/refer-friend';

const IndexClientsReferFriendHistory = () => {
  const [isMobileModalOpen, setMobileModalOpen] = useState(false);
  const [isDesktopModalOpen, setDesktopModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { userDetails } = useAppSelector((state) => state.auth);

  const shareUrl = `https://${APP_URL}?r=${userDetails?.referralCode}`;
  const copyUrl = () => {
    copyToClipboard(shareUrl);
    toast.success('URL Copied to Clipboard');
  };

  const router = useRouter();

  // Simulate loading state
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800);
    return () => clearTimeout(timer);
  }, []);

  const handleInviteModal = () => {
    if (isLoading) {
      return;
    }
    if (window && window.innerWidth <= 768) {
      setMobileModalOpen(true);
    } else {
      setDesktopModalOpen(true);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        when: 'beforeChildren',
        staggerChildren: 0.2,
        duration: 0.5,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  const buttonHoverAnimation = {
    scale: 1.05,
    transition: { duration: 0.2 },
  };

  const buttonTapAnimation = {
    scale: 0.95,
  };

  return (
    <>
      <CommonHeader
        headline='My Earnings'
        subHeading={<span>Refer Friends</span>}
      />
      <motion.section
        animate='visible'
        className='max-w-[1280px] min-[1280px]:mx-auto'
        initial='hidden'
        onClick={(e) => {
          e.stopPropagation();
        }}
        transition={{ duration: 0.5 }}
        variants={containerVariants}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'User Page', link: '/user' },
              { title: 'Refer Friends', link: '/' },
            ]}
            rootClass='sticky top-[104px] z-[9]'
          />
          <div className='w-full flex z-[0] relative'>
            <MyEarningsSidenav activeNavId={8} />
            <motion.div
              animate={{ opacity: 1 }}
              className='bg-[#E0E0E0] dark:bg-[#1F222A] w-full ml-[2px] pb-[80px]'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <motion.div className='pt-[12px] px-[6px] lg:px-[15px] xl:px-[30px] flex flex-col gap-y-[10px]'>
                <motion.div
                  animate={{ opacity: 1, y: 0 }}
                  className='hidden lg:flex justify-end mt-[30px] gap-x-[10px]'
                  initial={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <motion.div
                    whileHover={buttonHoverAnimation}
                    whileTap={buttonTapAnimation}
                  >
                    <ThemeButton
                      className='!w-[110px] !text-[10px] !bg-transparent hover:!bg-primary hover:!text-white !text-primary border-[1px] border-primary'
                      onClick={handleInviteModal}
                      text='Invite Friend'
                    />
                  </motion.div>
                  <motion.div
                    whileHover={buttonHoverAnimation}
                    whileTap={buttonTapAnimation}
                  >
                    <ThemeButton
                      className='!w-[110px] !text-[10px] hover:!bg-primary hover:!text-white !bg-white !text-black border-[1px] border-primary'
                      onClick={() => router.push('/referral-history')}
                      text='Referral History'
                    />
                  </motion.div>
                </motion.div>
                <ReferFriendBanner />
              </motion.div>
              {/* -----------------invite steps---------------------- */}
              <motion.div
                className='relative mt-[30px] lg:mt-[50px] px-[6px] lg:flex lg:justify-center lg:items-between lg:gap-x-[20px]'
                variants={itemVariants}
              >
                <motion.div
                  className='flex items-center lg:flex-col'
                  variants={itemVariants}
                  whileHover={{ scale: 1.05 }}
                >
                  <Image
                    alt='refer friends'
                    className='w-[130px] xl:w-[207px] h-auto shrink-0 order-2 lg:order-1 ml-[10px] lg:ml-0'
                    height={154}
                    src='/img/my-earnings/invite1.png'
                    title='refer friends'
                    width={207}
                  />
                  <p className='text-[10px] font-medium text-blackWhite order-1 lg:order-2 lg:max-w-[160px] lg:mt-[40px]'>
                    Invite your friends through Email, Chat Apps or Social Media
                  </p>
                </motion.div>
                <Image
                  alt='arrow'
                  className='lg:w-auto mt-[10px] lg:-mt-4 hidden lg:inline-block'
                  src={long_arrow}
                  title='arrow'
                />
                <DottedLine3 className='w-[135px] h-[122px] text-blackWhite absolute top-[80px] left-[75px] lg:hidden' />
                <motion.div
                  className='flex items-center mt-[70px] lg:mt-0 lg:flex-col lg:justify-center'
                  variants={itemVariants}
                  whileHover={{ scale: 1.05 }}
                >
                  <Image
                    alt='invite illustration1'
                    className='w-[130px] lg:w-[150px] xl:w-[220px] h-auto shrink-0'
                    height={154}
                    src='/img/my-earnings/invite2.png'
                    title='invite illustration1'
                    width={207}
                  />
                  <p className='text-[10px] font-medium text-blackWhite ml-[10px] lg:ml-0 lg:max-w-[160px] lg:mt-[40px]'>
                    Invited your friends shops through ICB
                  </p>
                </motion.div>
                <Image
                  alt='arrow'
                  className='lg:w-auto mt-[10px] lg:-mt-4 hidden lg:inline-block'
                  src={long_arrow}
                  title='arrow'
                />
                <DottedLine4 className='w-[135px] h-[118px] text-blackWhite absolute top-[250px] left-[75px] lg:hidden' />
                <motion.div
                  className='flex items-center mt-[110px] lg:mt-0 lg:flex-col'
                  variants={itemVariants}
                  whileHover={{ scale: 1.05 }}
                >
                  <Image
                    alt='invite illustration1'
                    className='w-[130px] xl:w-[207px] h-auto shrink-0 order-2 lg:order-1 ml-[10px]'
                    height={154}
                    src='/img/my-earnings/invite3.png'
                    title='invite illustration1'
                    width={207}
                  />
                  <p className='text-[10px] font-medium text-blackWhite order-2 lg:max-w-[160px] lg:mt-[40px]'>
                    You get 10% of your friend's earning when your friend earns
                    confirmed cashback.
                  </p>
                  <p className='text-[8px] text-white/80 font-medium mt-[5px] lg:mt-[8px]'>
                    *T&C Apply
                  </p>
                </motion.div>
              </motion.div>

              {/* ------------------highlights----------------------- */}
              <motion.div
                className='flex flex-wrap justify-center gap-x-[10px] gap-y-[10px] lg:gap-x-[30px] mt-[48px]'
                variants={itemVariants}
              >
                <motion.div
                  variants={itemVariants}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <AboutHighlightCard
                    caption='Users'
                    highlightedText='1,02,356'
                    icon={
                      <Image
                        alt='icon'
                        className='w-[8px] h-[12px] lg:w-[18px] lg:h-[27px]'
                        height={27}
                        src='/svg/about-us/person.svg'
                        title='icon'
                        width={18}
                      />
                    }
                  />
                </motion.div>
                <motion.div
                  variants={itemVariants}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <AboutHighlightCard
                    caption='Invited Friends'
                    highlightedText='1,02,356'
                    icon={
                      <Image
                        alt='icon'
                        className='w-[15px] h-[12px] lg:w-[34px] lg:h-[26px]'
                        height={26}
                        src='/svg/about-us/people.svg'
                        title='icon'
                        width={34}
                      />
                    }
                  />
                </motion.div>
                <motion.div
                  variants={itemVariants}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <AboutHighlightCard
                    caption='Cashback Given'
                    highlightedText='₹ 1,02,356'
                    icon={
                      <Image
                        alt='icon'
                        className='w-[16px] h-[16px] lg:w-[35px] lg:h-[35px]'
                        height={35}
                        src='/svg/about-us/cashback-given.svg'
                        title='icon'
                        width={35}
                      />
                    }
                  />
                </motion.div>
              </motion.div>
              <motion.div
                className='flex justify-center'
                variants={itemVariants}
                whileHover={buttonHoverAnimation}
                whileTap={buttonTapAnimation}
              >
                <ThemeButton
                  className='!w-[116px] lg:!w-[256px] uppercase !mt-[32px] mx-auto'
                  onClick={handleInviteModal}
                  text='Invite Friend'
                />
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </motion.section>

      <AnimatePresence>
        <BottomDrawer
          heightClass='40svh'
          onClose={() => setMobileModalOpen(false)}
          open={isMobileModalOpen}
          title='Invite Friends'
          titleIcon={<></>}
          topClass='calc(100% - 40svh)'
        >
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className='pt-[26px]'
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.3 }}
          >
            <motion.div
              animate={{ opacity: 1 }}
              className='w-full h-[36px] flex-center rounded-[5px] bg-[#FFC554] text-black text-[8px] lg:text-xs font-medium'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              whileHover={{ scale: 1.02 }}
            >
              {shareUrl}
            </motion.div>
            <motion.div
              animate={{ opacity: 1 }}
              className='flex justify-center gap-x-[12px] text-white mt-[30px]'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <RWebShare
                data={{
                  text: `Love getting cashback on your purchases? Me too! 🙌 Join Indian Cashback with my referral link and start earning. It's a win-win! 😉 👉  `,
                  url: shareUrl,
                  title: 'Share your referral link with friends!',
                }}
                key={'share_page_1'}
              >
                <motion.button
                  className='bg-primary flex-center w-[127px] h-[35px] rounded-[5px] uppercase'
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Share Invite{' '}
                  <ShareSVG className='w-[13px] text-white ml-[7px]' />
                </motion.button>
              </RWebShare>
              <motion.button
                className='bg-primary flex-center w-[127px] h-[35px] rounded-[5px] uppercase'
                onClick={() => copyUrl()}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Copy Link <CopySVG className='w-[18px] text-white ml-[7px]' />
              </motion.button>
            </motion.div>
          </motion.div>
        </BottomDrawer>
      </AnimatePresence>
      <AnimatePresence>
        {isDesktopModalOpen && (
          <Modal
            cancelText=''
            footer={<></>}
            okText=''
            onCancel={() => setDesktopModalOpen(false)}
            open={isDesktopModalOpen}
            title='Invite Friends'
          >
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='pt-[26px]'
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                animate={{ opacity: 1 }}
                className='w-full h-[36px] flex-center rounded-[5px] bg-[#FFC554] text-black text-[8px] lg:text-xs font-medium'
                initial={{ opacity: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                whileHover={{ scale: 1.02 }}
              >
                {shareUrl}
              </motion.div>
              <motion.div
                animate={{ opacity: 1 }}
                className='flex justify-center gap-x-[12px] text-white mt-[30px]'
                initial={{ opacity: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                <RWebShare
                  data={{
                    text: `Love getting cashback on your purchases? Me too! 🙌 Join Indian Cashback with my referral link and start earning. It's a win-win! 😉 👉  `,
                    url: shareUrl,
                    title: 'Share your referral link with friends!',
                  }}
                  key={'share_page_1'}
                >
                  <motion.button
                    className='bg-primary flex-center w-[127px] h-[35px] rounded-[5px] uppercase'
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Share Invite
                    <ShareSVG className='w-[13px] text-white ml-[7px]' />
                  </motion.button>
                </RWebShare>
                <motion.button
                  className='bg-primary flex-center w-[127px] h-[35px] rounded-[5px] uppercase'
                  onClick={() => copyUrl()}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Copy Link <CopySVG className='w-[18px] text-white ml-[7px]' />
                </motion.button>
              </motion.div>
            </motion.div>
          </Modal>
        )}
      </AnimatePresence>
    </>
  );
};
export default IndexClientsReferFriendHistory;
