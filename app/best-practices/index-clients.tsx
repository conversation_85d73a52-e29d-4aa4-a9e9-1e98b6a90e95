'use client';
import React from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import CommonHeader from '../components/headers/common-header';

const IndexClients = () => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        when: 'beforeChildren',
        staggerChildren: 0.2,
        duration: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: 'spring', stiffness: 300, damping: 24 },
    },
  };

  const cardVariants = {
    hidden: { scale: 0.9, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: { type: 'spring', stiffness: 300, damping: 20 },
    },
    hover: {
      scale: 1.03,
      boxShadow: '0px 8px 20px rgba(0, 0, 0, 0.1)',
      transition: { duration: 0.3 },
    },
  };

  const practiceCards = [
    {
      title: 'Maximizing Your Cashback',
      icon: '/img/maximize-cashback.png',
      iconFallback: '💰',
      bgColor:
        'bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-700 dark:to-purple-600',
      tips: [
        'Always start your shopping journey from IndianCashback.com',
        'Clear your browser cookies before clicking through to a store',
        'Complete your purchase in the same browsing session',
        'Avoid using coupon codes from other websites unless specified',
        'Check for exclusive cashback rates during special promotions',
      ],
    },
    {
      title: 'Shopping Securely Online',
      icon: '/img/secure-shopping.png',
      iconFallback: '🔒',
      bgColor:
        'bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-700 dark:to-blue-600',
      tips: [
        'Shop only on secure websites (look for HTTPS in the URL)',
        'Use strong, unique passwords for each shopping account',
        'Consider using a credit card instead of a debit card for better protection',
        'Keep your devices and browsers updated',
        'Be cautious of deals that seem too good to be true',
      ],
    },
    {
      title: 'Smart Shopping Strategies',
      icon: '/img/smart-shopping.png',
      iconFallback: '🛒',
      bgColor:
        'bg-gradient-to-br from-green-100 to-green-200 dark:from-green-700 dark:to-green-600',
      tips: [
        'Compare prices across multiple stores before purchasing',
        'Look for seasonal sales and special promotions',
        'Read product reviews and ratings before buying',
        'Check return policies and warranty information',
        'Consider the total cost including shipping and taxes',
      ],
    },
  ];

  return (
    <>
      <CommonHeader headline='Best Practices for Online Shopping' />
      <motion.div
        animate='visible'
        className='min-h-screen overflow-hidden max-w-[1280px] min-[1280px]:mx-auto bg-[#f5f5f5] dark:bg-[#2d2e32] px-4 lg:px-8 xl:px-12 pb-10'
        initial='hidden'
        variants={containerVariants}
      >
        <motion.div
          className='mt-6 lg:mt-12 text-center'
          variants={itemVariants}
        >
          <motion.h1
            animate={{ scale: [1, 1.03, 1] }}
            className='text-2xl lg:text-4xl font-bold mb-4 text-primary dark:text-white'
            transition={{ duration: 2, repeat: Infinity, repeatDelay: 5 }}
          >
            Shop Smarter with IndianCashback
          </motion.h1>
          <motion.p
            className='text-base lg:text-lg mb-8 text-gray-700 dark:text-gray-300 max-w-3xl mx-auto'
            variants={itemVariants}
          >
            Follow these best practices to ensure a safe, secure, and rewarding
            online shopping experience while maximizing your cashback rewards.
          </motion.p>
        </motion.div>

        {/* Main content with cards */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 mt-8'>
          {practiceCards.map((card, index) => (
            <motion.div
              animate='visible'
              className={`rounded-xl shadow-md overflow-hidden ${card.bgColor}`}
              initial='hidden'
              key={card.title}
              transition={{ delay: index * 0.1 }}
              variants={cardVariants}
              whileHover='hover'
            >
              <div className='p-6'>
                <div className='flex items-center mb-4'>
                  <div className='w-12 h-12 rounded-full bg-white flex items-center justify-center mr-4 shadow-sm'>
                    {card.icon ? (
                      <Image
                        alt={card.title}
                        className='object-contain'
                        height={32}
                        onError={(e) => {
                          // Fallback to emoji if image fails to load
                          e.currentTarget.style.display = 'none';
                          if (e.currentTarget.parentElement) {
                            e.currentTarget.parentElement.innerHTML =
                              card.iconFallback;
                          }
                        }}
                        src={card.icon}
                        width={32}
                      />
                    ) : (
                      <span className='text-2xl'>{card.iconFallback}</span>
                    )}
                  </div>
                  <h2 className='text-xl font-bold text-gray-800 dark:text-white'>
                    {card.title}
                  </h2>
                </div>

                <ul className='space-y-3 mt-4'>
                  {card.tips.map((tip, tipIndex) => (
                    <motion.li
                      animate={{ opacity: 1, x: 0 }}
                      className='flex items-start'
                      initial={{ opacity: 0, x: -10 }}
                      key={tipIndex}
                      transition={{ delay: index * 0.1 + tipIndex * 0.05 }}
                    >
                      <span className='w-5 h-5 bg-primary dark:bg-purple-600 rounded-full flex-shrink-0 flex items-center justify-center text-white text-xs mr-3 mt-0.5'>
                        ✓
                      </span>
                      <span className='text-sm lg:text-base text-gray-700 dark:text-gray-200'>
                        {tip}
                      </span>
                    </motion.li>
                  ))}
                </ul>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Additional tips section */}
        <motion.div
          className='mt-12 bg-white dark:bg-[#32363F] rounded-xl shadow-lg p-6 lg:p-8'
          variants={itemVariants}
        >
          <h2 className='text-xl lg:text-2xl font-bold mb-6 text-primary dark:text-white'>
            Additional Tips for Maximizing Your Savings
          </h2>

          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <motion.div
              className='bg-gray-50 dark:bg-gray-800 p-5 rounded-lg'
              transition={{ duration: 0.2 }}
              whileHover={{ scale: 1.02 }}
            >
              <h3 className='text-lg font-semibold mb-3 text-gray-800 dark:text-white'>
                Timing Your Purchases
              </h3>
              <p className='text-gray-700 dark:text-gray-300 text-sm lg:text-base'>
                Many online stores offer seasonal sales and special promotions.
                Plan your major purchases around these events to maximize your
                savings and cashback opportunities.
              </p>
            </motion.div>

            <motion.div
              className='bg-gray-50 dark:bg-gray-800 p-5 rounded-lg'
              transition={{ duration: 0.2 }}
              whileHover={{ scale: 1.02 }}
            >
              <h3 className='text-lg font-semibold mb-3 text-gray-800 dark:text-white'>
                Combine Offers Strategically
              </h3>
              <p className='text-gray-700 dark:text-gray-300 text-sm lg:text-base'>
                Look for opportunities to stack cashback with store discounts,
                credit card rewards, and other promotions for maximum savings on
                your purchases.
              </p>
            </motion.div>
          </div>
        </motion.div>

        {/* FAQ-style section */}
        <motion.div
          className='mt-12 bg-white dark:bg-[#32363F] rounded-xl shadow-lg p-6 lg:p-8'
          variants={itemVariants}
        >
          <h2 className='text-xl lg:text-2xl font-bold mb-6 text-primary dark:text-white'>
            Frequently Asked Questions
          </h2>

          <div className='space-y-4'>
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='border-b border-gray-200 dark:border-gray-700 pb-4'
              initial={{ opacity: 0, y: 10 }}
              transition={{ delay: 0.2 }}
            >
              <h3 className='text-lg font-semibold mb-2 text-gray-800 dark:text-white'>
                Why isn't my cashback tracking?
              </h3>
              <p className='text-gray-700 dark:text-gray-300 text-sm lg:text-base'>
                Cashback may not track if you have ad blockers enabled, use
                multiple browser tabs, or apply coupon codes from other
                websites. Always start fresh from IndianCashback.com and
                complete your purchase in the same session.
              </p>
            </motion.div>

            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='border-b border-gray-200 dark:border-gray-700 pb-4'
              initial={{ opacity: 0, y: 10 }}
              transition={{ delay: 0.3 }}
            >
              <h3 className='text-lg font-semibold mb-2 text-gray-800 dark:text-white'>
                How long does it take for cashback to appear in my account?
              </h3>
              <p className='text-gray-700 dark:text-gray-300 text-sm lg:text-base'>
                Cashback typically appears as 'Pending' within 48 hours of your
                purchase. The time it takes to become 'Confirmed' varies by
                store, usually between 30-90 days, depending on their return and
                confirmation policies.
              </p>
            </motion.div>
          </div>
        </motion.div>

        {/* Call to action */}
        <motion.div
          className='mt-12 text-center'
          variants={itemVariants}
          whileHover={{ scale: 1.02 }}
        >
          <div className='bg-primary dark:bg-primaryDark text-white rounded-xl p-6 lg:p-8 shadow-lg'>
            <h2 className='text-xl lg:text-2xl font-bold mb-4'>
              Ready to Start Saving?
            </h2>
            <p className='mb-6 max-w-2xl mx-auto'>
              Apply these best practices to your online shopping and start
              earning cashback on every purchase!
            </p>
            <motion.a
              className='inline-block bg-white text-primary font-bold py-3 px-8 rounded-full shadow-md'
              href='/'
              whileHover={{
                scale: 1.05,
                boxShadow: '0px 5px 15px rgba(0, 0, 0, 0.2)',
              }}
              whileTap={{ scale: 0.95 }}
            >
              Start Shopping Now
            </motion.a>
          </div>
        </motion.div>
      </motion.div>
    </>
  );
};

export default IndexClients;
