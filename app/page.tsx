import StoryPreviewsContainer from './components/landing/stories/story-previews-container';
import HeroSlider from './components/landing/hero-slider/index';
import QuickAccess from './components/landing/quick-access/quick-access';
// import IcbCardShowCase from './components/landing/icb-card-showcase';
import StoresByPercentage from '@/app/components/landing/stores-cb-percentage/index';
import TrendingOffers from '@/app/components/landing/trending-offers/index';
import OnGoingSaleOffers from '@/app/components/landing/on-going-sale-offers/index';
import Testimonials from '@/app/components/landing/testimonials/index';
import ICBCardShowcase2 from './components/landing/icb-card-showcase2';
import MissedOffers from './components/landing/missed-offers/index';
import TrendingCategories from './components/landing/trending-categories/index';
import QuickLinks from './components/landing/quick-links/index';
import ErrorBoundary from './components/error-boundry';
import GlobalSearchMobileLeftPanel from './components/landing/search/global-search-mobile-modal';
import SplashScreen from './components/atoms/splash-screen';
import { BASE_URL } from '@/config';
import { cookies } from 'next/headers';
import fetchWrapper from '@/utils/fetch-wrapper';
import type {
  BannerResponse,
  CategorizedOffers,
  CategoryResponse,
  CategoryStoresResponse,
  QuickAccessResponseItem,
  ResponseMobileStories,
  StoresByCbContextResponse,
  TestimonialResponseType,
} from '@/services/api/data-contracts';
import FindByCategories from '@/app/components/landing/find-by-category/index';
import CheckSignupQuery from './components/auth/check-signup-query';
import TokenVerifier from './components/auth/token-verifier';
import type { Metadata } from 'next';
import ReferFriendBanner from './components/my-earnings/refer-friend';

export const metadata: Metadata = {
  title: 'Shop & Save: Cashback, Discounts & Best Deals - IndianCashback',
  description:
    'Save on Every Purchase! Get the best cashback deals, free coupons & exclusive discounts on top Indian online stores. Shop smart with IndianCashback!',
  alternates: {
    canonical: 'https://www.indiancashback.com',
  },
  openGraph: {
    url: 'https://www.indiancashback.com',
    title: 'Shop & Save: Cashback, Discounts & Best Deals - IndianCashback',
    description:
      'Save on Every Purchase! Get the best cashback deals, free coupons & exclusive discounts on top Indian online stores. Shop smart with IndianCashback!',
  },
};

async function getHeroSliderData() {
  return await fetchWrapper<BannerResponse>(`${BASE_URL}/context/banner`);
}

async function getQuickAccessData() {
  return await fetchWrapper<QuickAccessResponseItem[]>(
    `${BASE_URL}/context/quick-access`
  );
}

async function getStoriesData() {
  return await fetchWrapper<ResponseMobileStories[]>(
    `${BASE_URL}/context/stories`
  );
}

async function getStoresByCat() {
  return await fetchWrapper<CategoryStoresResponse[]>(
    `${BASE_URL}/context/stores-by-cb-percent/find-by-category`
  );
}

async function getOffersData() {
  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return await fetchWrapper<CategorizedOffers>(`${BASE_URL}/context/offers`, {
    token: token?.value,
  });
}

async function getCategoriesData() {
  return await fetchWrapper<CategoryResponse[]>(
    `${BASE_URL}/context/category?trending=true`
  );
}

async function getStoresByCBPercentageData() {
  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return await fetchWrapper<StoresByCbContextResponse>(
    `${BASE_URL}/context/stores-by-cb-percent`,
    {
      token: token?.value,
    }
  );
}

async function getAllTestimonialsData() {
  return await fetchWrapper<TestimonialResponseType[]>(
    `${BASE_URL}/context/testimonials`
  );
}

export default async function Home() {
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  let data: any;
  try {
    data = await Promise.allSettled([
      getHeroSliderData(),
      getQuickAccessData(),
      getStoriesData(),
      getOffersData(),
      getCategoriesData(),
      getStoresByCBPercentageData(),
      getAllTestimonialsData(),
      getStoresByCat(),
    ]);
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  } catch (err: any) {
    console.log({ err });
  }

  const [
    heroSliderData,
    quickAccessData,
    storiesData,
    offersData,
    categoriesData,
    storesByCBPercentData,
    testimonialsData,
    storesByCatData,
  ] = data || [];

  return (
    <main className='min-h-screen'>
      <SplashScreen />
      <CheckSignupQuery />
      <TokenVerifier />
      <ErrorBoundary>
        <StoryPreviewsContainer
          promiseStatus={storiesData?.status}
          storiesData={storiesData?.value}
        />
      </ErrorBoundary>
      <QuickLinks />
      <HeroSlider
        heroSliderData={heroSliderData?.value}
        promiseStatus={heroSliderData?.status}
      />
      <FindByCategories
        promiseStatus={storesByCatData?.status}
        storesByCat={storesByCatData?.value}
      />
      {/* Always render TrendingOffers with either data or empty array */}
      <TrendingOffers
        promiseStatus={offersData?.status}
        trendingOffers={offersData?.value?.trendingOffers || []}
      />
      {/* Always render QuickAccess with either data or empty array */}
      <QuickAccess
        promiseStatus={quickAccessData?.status}
        quickAccessData={quickAccessData?.value || []}
      />
      {/* <IcbCardShowCase /> */}
      {/* Always render StoresByPercentage with either data or null */}
      <StoresByPercentage
        promiseStatus={storesByCBPercentData?.status}
        storesByCBPercent={storesByCBPercentData?.value || null}
      />
      {/* Always render OnGoingSaleOffers with either data or empty array */}
      <OnGoingSaleOffers
        onGoingOffersData={offersData?.value?.ongoingOffers || []}
        promiseStatus={offersData?.status}
      />
      <ICBCardShowcase2 />
      <ReferFriendBanner isLink={true} />
      {/* Always render MissedOffers with either data or empty array */}
      <MissedOffers
        missedOffers={offersData?.value?.expiredOffers || []}
        promiseStatus={offersData?.status}
      />
      {/* Always render Testimonials with either data or empty array */}
      <Testimonials
        promiseStatus={testimonialsData?.status}
        testimonials={testimonialsData?.value || []}
      />
      {/* Always render TrendingCategories with either data or empty array */}
      <TrendingCategories
        promiseStatus={categoriesData?.status}
        trendingCategories={categoriesData?.value || []}
      />
      {/* <Blogs /> */}
      <GlobalSearchMobileLeftPanel />
    </main>
  );
}

// revalidate every 60 seconds
export const revalidate = 60;
