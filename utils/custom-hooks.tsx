'use client';

import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { setLoginModalOpen } from '@/redux/slices/auth-slice';
import { useCallback, useEffect, useState, useRef } from 'react';
import { copyToClipboard } from './helpers';
import { toast } from 'react-toastify';
import { useDispatch } from 'react-redux';
import type { ReadonlyURLSearchParams } from 'next/navigation';
import {
  type ClickCreateResponse,
  ClickTypeTypeEnum,
} from '@/services/api/data-contracts';
import {
  setClickGeneratedData,
  setGlobalLoading,
  setProceedWithoutCb,
  setSelectedOffer,
  setShowClickRegisModal,
  setShowWarningModal,
} from '@/redux/slices/global-slice';
import fetchWrapper from './fetch-wrapper';
import { BASE_URL } from '@/config';

export interface GridOptions {
  minCardWidth?: number;
  maxCardWidth?: number;
  mobileGap?: number;
  desktopGap?: number;
  desktopBreakpoint?: number;
  dependencies?: React.DependencyList;
}

export const useResponsiveGrid = ({
  minCardWidth = 155,
  maxCardWidth = 250,
  mobileGap = 8,
  desktopGap = 24,
  dependencies = [],
}: {
  minCardWidth?: number;
  maxCardWidth?: number;
  mobileGap?: number;
  desktopGap?: number;
  dependencies?: React.DependencyList;
} = {}) => {
  const [gridColumns, setGridColumns] = useState(2);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const calculateColumns = () => {
      if (containerRef.current) {
        const containerWidth = containerRef.current.offsetWidth;
        const gap = window.innerWidth >= 1024 ? desktopGap : mobileGap;

        // Calculate how many cards can fit within the min/max constraints
        const maxColumns = Math.floor(
          (containerWidth + gap) / (minCardWidth + gap)
        );
        const minColumns = Math.ceil(
          (containerWidth + gap) / (maxCardWidth + gap)
        );

        // Determine optimal number of columns
        const optimalColumns = Math.max(
          1,
          Math.min(maxColumns, Math.max(minColumns, 2))
        );

        // Set the columns
        setGridColumns(optimalColumns);
      }
    };

    // Calculate on mount and when window resizes
    calculateColumns();
    const handleResize = () => calculateColumns();
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [minCardWidth, maxCardWidth, mobileGap, desktopGap, ...dependencies]);

  // Return a function to get grid props for convenience
  const getGridProps = () => ({
    className: `grid grid-cols-${Math.min(
      gridColumns,
      2
    )} gap-x-[8px] pt-[23px] gap-y-[16px] md:grid-cols-${Math.min(
      gridColumns,
      3
    )} lg:gap-[24px] transition-all duration-300`,
    ref: containerRef,
    style: {
      gridTemplateColumns: `repeat(${gridColumns}, minmax(${minCardWidth}px, 1fr))`,
      display: 'grid',
    },
  });

  return {
    gridColumns,
    containerRef,
    getGridProps,
  };
};

export const useCreateQueryString = (searchParams: ReadonlyURLSearchParams) => {
  // Get a new searchParams string by merging the current
  // searchParams with a provided key/value pair
  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams?.toString());
      if (value) {
        params.set(name, value);
      } else {
        params.delete(name);
      }
      return params?.toString();
    },
    [searchParams]
  );
  return createQueryString;
};

export const useCreateMultiQueryString = (
  searchParams: ReadonlyURLSearchParams
) => {
  // Get a new searchParams string by merging the current
  // searchParams with a provided key/value pair
  const createMultiQueryString = useCallback(
    (queries: Array<{ name: string; value: string }>) => {
      const params = new URLSearchParams(searchParams?.toString());
      for (const item of queries) {
        if (item.value) {
          params.set(item.name, item.value);
        } else {
          params.delete(item.name);
        }
      }
      return params?.toString();
    },
    [searchParams]
  );
  return createMultiQueryString;
};

export const useCopyCoupon = () => {
  const dispatch = useAppDispatch();
  const { isUserLogin } = useAppSelector((state) => state.auth);
  const generateClickData = useGenerateClickData();
  const handleCopyCoupon = async ({
    event,
    couponCode,
    uid,
  }: {
    event: React.MouseEvent<HTMLButtonElement, MouseEvent>;
    couponCode: string;
    uid: number;
  }) => {
    event.stopPropagation();

    // Dispatch the selected offer right away
    dispatch(
      setSelectedOffer({
        uid: uid,
        type: ClickTypeTypeEnum.Offer,
        couponCode,
      })
    );

    // Handle window opening and login actions in parallel
    const actions = [];

    // Open a new tab if the user is logged in and no offer warning
    if (isUserLogin) {
      actions.push(window.open('/redirecting', '_blank', 'noreferrer'));
      copyToClipboard(couponCode);
    }

    if (!isUserLogin) {
      // Set modal states for login
      dispatch(setProceedWithoutCb(true));
      return dispatch(setLoginModalOpen(true));
    }

    // Generate click data if the user is logged in and no warning
    actions.push(
      generateClickData({ uid: uid, type: ClickTypeTypeEnum.Offer })
    );

    // Run all actions in parallel
    await Promise.all(actions);

    toast.success('Successfully copied coupon code. Please wait...');

    //NOTE - actual code:
    // event.stopPropagation();
    // dispatch(setSelectedOffer({ uid, type: 'offer', couponCode }));
    // if (!isUserLogin) {
    //   //in non login state, coupon code can only be copy when user pressed on "Proceed to store" in login-sign-up modal
    //   dispatch(setProceedWithoutCb(true));
    //   generateClickData;
    //   dispatch(setLoginModalOpen(true));
    // } else {
    //   //only copy code when user is login
    //   copyToClipboard(couponCode);
    //   generateClickData({ uid, type: 'offer' });
    //   toast.success('Successfully copied coupon code. Please wait...');
    // }
    //}
  };
  return handleCopyCoupon;
};

export const useGenerateClickData = () => {
  const dispatch = useDispatch();
  const { isUserLogin } = useAppSelector((state) => state.auth);
  const generateClickData = async ({
    uid,
    type,
  }: {
    uid: number;
    type: ClickTypeTypeEnum;
  }) => {
    try {
      let endpoint = `${BASE_URL}/click/no-auth`;
      if (isUserLogin) {
        endpoint = '/api/proxy/click';
      }
      dispatch(setGlobalLoading(true));
      const res = await fetchWrapper<ClickCreateResponse>(endpoint, {
        method: 'POST',
        body: JSON.stringify({ uid, type }),
      });
      if (res.isActive) {
        dispatch(setShowClickRegisModal(true));
      }
      dispatch(setClickGeneratedData(res));

      //TODO - check for this promise use
      // Return a promise that resolves when localStorage is set
      return new Promise<ClickCreateResponse>((resolve) => {
        const updatedData = {
          ...res,
          previousUrl: window.location.href,
        };
        localStorage.setItem('clickGeneratedData', JSON.stringify(updatedData));
        resolve(res);
      });
      // return res;
      // window.open(`/redirecting`, '_blank', 'noreferrer');
    } catch (error) {
      console.log({ error });
      return toast.error('Something went wrong in generating click data.');
    } finally {
      console.log('finally');
      dispatch(setGlobalLoading(false));
      dispatch(setShowWarningModal(false));
      dispatch(setLoginModalOpen(false));
    }
  };

  return generateClickData;
};

export const useOverflowing = (containerRef: any, dependency?: any) => {
  const [isOverflowing, setIsOverflowing] = useState(false);

  useEffect(() => {
    const isOverflowing = () => {
      if (!containerRef.current) {
        return false;
      } // Container reference not available

      const container = containerRef.current;

      const isOverflowingHorizontally =
        container.scrollWidth > container.clientWidth;

      return isOverflowingHorizontally;
    };

    setIsOverflowing(isOverflowing());
  }, [containerRef, dependency]);

  return isOverflowing;
};

export const useIsIOSDevice = () => {
  const [isIOS, setIsIOS] = useState(false);

  useEffect(() => {
    const userAgent = window.navigator.userAgent.toLowerCase();
    const isIOSDevice = /iphone|ipad|ipod/.test(userAgent);
    setIsIOS(isIOSDevice);
  }, []);

  return isIOS;
};
